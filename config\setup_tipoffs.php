<?php
/**
 * Database Setup Script for Tip-offs System
 * Run this file to add tip-offs functionality to CRMS
 */

require_once 'database.php';

try {
    $pdo = getDBConnection();
    
    // Create tip_offs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tip_offs (
            tip_id INT AUTO_INCREMENT PRIMARY KEY,
            tip_number VARCHAR(50) UNIQUE NOT NULL,
            reporter_type ENUM('anonymous', 'registered') NOT NULL DEFAULT 'anonymous',
            citizen_id INT NULL,
            tip_type VARCHAR(100) NOT NULL,
            tip_description TEXT NOT NULL,
            incident_date DATE NULL,
            incident_location VARCHAR(200) NULL,
            suspect_info TEXT NULL,
            case_visibility ENUM('public', 'private') DEFAULT 'public',
            status ENUM('new', 'under_investigation', 'resolved', 'dismissed') DEFAULT 'new',
            assigned_officer_id INT NULL,
            admin_notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON>OR<PERSON>G<PERSON> KEY (citizen_id) REFERENCES users(user_id) ON DELETE SET NULL,
            FOREIGN KEY (assigned_officer_id) REFERENCES users(user_id) ON DELETE SET NULL
        )
    ");
    
    // Create tip_off_updates table for tracking progress
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tip_off_updates (
            update_id INT AUTO_INCREMENT PRIMARY KEY,
            tip_number VARCHAR(50) NOT NULL,
            update_type ENUM('status_change', 'assignment', 'investigation_note', 'resolution') NOT NULL,
            update_description TEXT NOT NULL,
            updated_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES users(user_id)
        )
    ");
    
    echo "Tip-offs system setup completed successfully!<br>";
    echo "New tables created:<br>";
    echo "- tip_offs<br>";
    echo "- tip_off_updates<br>";
    echo "<br><a href='../index.php'>Go to Home Page</a>";
    
} catch (PDOException $e) {
    die("Tip-offs setup failed: " . $e->getMessage());
}
?>
