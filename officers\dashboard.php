<?php
require_once '../config/database.php';
session_start();
requireRole(['officer']);

// Get dashboard statistics for this officer
try {
    $pdo = getDBConnection();
    
    // Get case statistics for this officer
    $stmt = $pdo->prepare("SELECT 
        COUNT(*) as total_cases,
        SUM(CASE WHEN case_status = 'solved' THEN 1 ELSE 0 END) as solved_cases,
        SUM(CASE WHEN case_status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_cases,
        SUM(CASE WHEN case_status = 'unresolved' THEN 1 ELSE 0 END) as unresolved_cases
        FROM criminal_records WHERE created_by = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $case_stats = $stmt->fetch();
    
    // Get recent cases created by this officer
    $stmt = $pdo->prepare("SELECT * FROM criminal_records 
        WHERE created_by = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_cases = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = "Failed to load dashboard data.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Officer Dashboard - CRMS</title>
    <style>
        .stat-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
        }
        .nav-link {
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-link:hover {
            background-color: #dc2626;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Officer Dashboard</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-white">Welcome, Officer <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Quick Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="new_record.php" class="nav-link bg-green-700">File New Record</a>
                <a href="manage_records.php" class="nav-link bg-gray-700">My Cases</a>
                <a href="search_records.php" class="nav-link bg-gray-700">Search Records</a>
                <a href="reports.php" class="nav-link bg-gray-700">Generate Reports</a>
                <a href="profile.php" class="nav-link bg-gray-700">My Profile</a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card">
                <h3 class="text-red-500 text-lg font-semibold mb-2">My Total Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['total_cases'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-green-500 text-lg font-semibold mb-2">Solved Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['solved_cases'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-yellow-500 text-lg font-semibold mb-2">Ongoing Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['ongoing_cases'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-red-500 text-lg font-semibold mb-2">Unresolved Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['unresolved_cases'] ?? 0; ?></p>
            </div>
        </div>

        <!-- Recent Cases -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
            <h2 class="text-white text-xl font-bold mb-4">My Recent Cases</h2>
            <div class="overflow-x-auto">
                <table class="w-full text-white">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-2">Case Number</th>
                            <th class="text-left py-2">Suspect Name</th>
                            <th class="text-left py-2">Crime Type</th>
                            <th class="text-left py-2">Status</th>
                            <th class="text-left py-2">Incident Date</th>
                            <th class="text-left py-2">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($recent_cases)): ?>
                            <?php foreach ($recent_cases as $case): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-700">
                                    <td class="py-2"><?php echo htmlspecialchars($case['case_number']); ?></td>
                                    <td class="py-2"><?php echo htmlspecialchars($case['suspect_name']); ?></td>
                                    <td class="py-2"><?php echo htmlspecialchars($case['crime_type']); ?></td>
                                    <td class="py-2">
                                        <span class="px-2 py-1 rounded text-xs
                                            <?php 
                                            switch($case['case_status']) {
                                                case 'solved': echo 'bg-green-600'; break;
                                                case 'ongoing': echo 'bg-yellow-600'; break;
                                                case 'paused': echo 'bg-blue-600'; break;
                                                case 'unresolved': echo 'bg-red-600'; break;
                                                default: echo 'bg-gray-600';
                                            }
                                            ?>">
                                            <?php echo ucfirst($case['case_status']); ?>
                                        </span>
                                    </td>
                                    <td class="py-2"><?php echo date('M d, Y', strtotime($case['incident_date'])); ?></td>
                                    <td class="py-2">
                                        <a href="view_record.php?id=<?php echo $case['record_id']; ?>" 
                                           class="text-blue-400 hover:text-blue-300 mr-2">View</a>
                                        <a href="edit_record.php?id=<?php echo $case['record_id']; ?>" 
                                           class="text-green-400 hover:text-green-300">Edit</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4 text-gray-400">No cases found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h3 class="text-white text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="new_record.php" class="block bg-green-600 text-white text-center py-2 rounded hover:bg-green-700">
                        File New Criminal Record
                    </a>
                    <a href="search_records.php" class="block bg-blue-600 text-white text-center py-2 rounded hover:bg-blue-700">
                        Search Existing Records
                    </a>
                    <a href="reports.php" class="block bg-purple-600 text-white text-center py-2 rounded hover:bg-purple-700">
                        Generate Case Report
                    </a>
                </div>
            </div>
            
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h3 class="text-white text-lg font-bold mb-4">System Information</h3>
                <div class="text-gray-300 space-y-2">
                    <p><strong>Officer ID:</strong> <?php echo $_SESSION['user_id']; ?></p>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['user_email']); ?></p>
                    <p><strong>Last Login:</strong> <?php echo date('M d, Y H:i'); ?></p>
                    <p><strong>System Status:</strong> <span class="text-green-400">Online</span></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
