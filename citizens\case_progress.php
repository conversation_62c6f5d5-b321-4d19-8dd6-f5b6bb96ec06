<?php
require_once '../config/database.php';
session_start();
requireRole(['citizen']);

$search_term = '';
$cases = [];

// Handle search
if (isset($_GET['search'])) {
    $search_term = sanitizeInput($_GET['search']);
}

// Get all cases for public viewing (general case progress)
try {
    $pdo = getDBConnection();
    
    if (!empty($search_term)) {
        $stmt = $pdo->prepare("SELECT cr.case_number, cr.suspect_name, cr.crime_type, cr.case_status, 
            cr.incident_date, cr.incident_location, cr.created_at, cr.updated_at
            FROM criminal_records cr 
            WHERE cr.case_number LIKE ? OR cr.suspect_name LIKE ? OR cr.crime_type LIKE ? OR cr.incident_location LIKE ?
            ORDER BY cr.updated_at DESC");
        $search_param = "%$search_term%";
        $stmt->execute([$search_param, $search_param, $search_param, $search_param]);
    } else {
        $stmt = $pdo->query("SELECT cr.case_number, cr.suspect_name, cr.crime_type, cr.case_status, 
            cr.incident_date, cr.incident_location, cr.created_at, cr.updated_at
            FROM criminal_records cr 
            ORDER BY cr.updated_at DESC LIMIT 50");
    }
    
    $cases = $stmt->fetchAll();
} catch (PDOException $e) {
    $error_message = "Failed to load case information.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Case Progress Tracking - CRMS Citizen</title>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Case Progress Tracking</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <!-- Information Notice -->
        <div class="bg-blue-900 border border-blue-600 rounded-lg p-4 mb-6">
            <h3 class="text-blue-300 font-bold mb-2">📋 Public Case Information</h3>
            <p class="text-blue-200 text-sm">
                This page shows general information about ongoing and resolved cases for public awareness. 
                Personal details and sensitive information are not displayed. For specific inquiries about cases, 
                please contact the Lesotho Police Force directly.
            </p>
        </div>

        <!-- Search and Filters -->
        <div class="mb-6 flex flex-wrap gap-4 items-center justify-between">
            <div class="flex gap-4">
                <a href="dashboard.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Back to Dashboard
                </a>
                <a href="my_complaints.php" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    My Complaints
                </a>
            </div>
            
            <form method="GET" class="flex gap-2">
                <input type="text" name="search" placeholder="Search cases by case number, crime type, or location..." 
                       value="<?php echo htmlspecialchars($search_term); ?>"
                       class="px-4 py-2 bg-gray-700 text-white border border-gray-600 rounded focus:border-red-500 focus:outline-none w-80">
                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Search</button>
                <?php if (!empty($search_term)): ?>
                    <a href="case_progress.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">Clear</a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Cases Table -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
            <h2 class="text-white text-xl font-bold mb-4">
                Case Progress Information
                <?php if (!empty($search_term)): ?>
                    <span class="text-sm text-gray-400">(Search results for: "<?php echo htmlspecialchars($search_term); ?>")</span>
                <?php endif; ?>
            </h2>
            
            <div class="overflow-x-auto">
                <table class="w-full text-white text-sm">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-3">Case Number</th>
                            <th class="text-left py-3">Crime Type</th>
                            <th class="text-left py-3">Status</th>
                            <th class="text-left py-3">Location</th>
                            <th class="text-left py-3">Incident Date</th>
                            <th class="text-left py-3">Last Updated</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($cases)): ?>
                            <?php foreach ($cases as $case): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-700">
                                    <td class="py-3 font-mono"><?php echo htmlspecialchars($case['case_number']); ?></td>
                                    <td class="py-3"><?php echo htmlspecialchars($case['crime_type']); ?></td>
                                    <td class="py-3">
                                        <span class="px-2 py-1 rounded text-xs
                                            <?php 
                                            switch($case['case_status']) {
                                                case 'solved': echo 'bg-green-600'; break;
                                                case 'ongoing': echo 'bg-yellow-600'; break;
                                                case 'paused': echo 'bg-blue-600'; break;
                                                case 'unresolved': echo 'bg-red-600'; break;
                                                default: echo 'bg-gray-600';
                                            }
                                            ?>">
                                            <?php echo ucfirst($case['case_status']); ?>
                                        </span>
                                    </td>
                                    <td class="py-3"><?php echo htmlspecialchars($case['incident_location']); ?></td>
                                    <td class="py-3"><?php echo date('M d, Y', strtotime($case['incident_date'])); ?></td>
                                    <td class="py-3"><?php echo date('M d, Y', strtotime($case['updated_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-8 text-gray-400">
                                    <?php echo !empty($search_term) ? 'No cases found matching your search.' : 'No case information available.'; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Case Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-yellow-400 font-semibold">Ongoing</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($cases, function($c) { return $c['case_status'] == 'ongoing'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-green-400 font-semibold">Solved</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($cases, function($c) { return $c['case_status'] == 'solved'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-blue-400 font-semibold">Paused</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($cases, function($c) { return $c['case_status'] == 'paused'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-red-400 font-semibold">Unresolved</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($cases, function($c) { return $c['case_status'] == 'unresolved'; })); ?>
                </p>
            </div>
        </div>

        <!-- Information Section -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h3 class="text-white text-lg font-bold mb-4">Case Status Guide</h3>
                <div class="space-y-3 text-sm">
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 bg-yellow-600 text-white rounded text-xs">Ongoing</span>
                        <p class="text-gray-300">Case is currently under investigation</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 bg-green-600 text-white rounded text-xs">Solved</span>
                        <p class="text-gray-300">Case has been resolved successfully</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 bg-blue-600 text-white rounded text-xs">Paused</span>
                        <p class="text-gray-300">Investigation temporarily suspended</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="px-2 py-1 bg-red-600 text-white rounded text-xs">Unresolved</span>
                        <p class="text-gray-300">Case remains unsolved</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h3 class="text-white text-lg font-bold mb-4">Contact Information</h3>
                <div class="space-y-2 text-sm text-gray-300">
                    <p><strong>Emergency:</strong> 911</p>
                    <p><strong>Non-Emergency:</strong> +266 2231-2345</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Address:</strong> Police Headquarters, Maseru</p>
                    <p><strong>Office Hours:</strong> Mon-Fri 8:00 AM - 5:00 PM</p>
                    <div class="mt-4 p-3 bg-yellow-900 border border-yellow-600 rounded">
                        <p class="text-yellow-200 text-xs">
                            <strong>Note:</strong> For specific case inquiries or to report information related to any case, 
                            please contact the police directly using the information above.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
