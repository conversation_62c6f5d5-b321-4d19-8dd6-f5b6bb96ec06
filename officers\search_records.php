<?php
require_once '../config/database.php';
session_start();
requireRole(['officer']);

$search_term = '';
$search_type = '';
$status_filter = '';
$records = [];

// Handle search
if ($_SERVER['REQUEST_METHOD'] == 'GET' && !empty($_GET['search'])) {
    $search_term = sanitizeInput($_GET['search']);
    $search_type = $_GET['search_type'] ?? 'all';
    $status_filter = $_GET['status_filter'] ?? '';
    
    try {
        $pdo = getDBConnection();
        
        // Build search query based on search type
        $sql = "SELECT cr.*, u.first_name, u.last_name, ao.first_name as officer_fname, ao.last_name as officer_lname
                FROM criminal_records cr 
                LEFT JOIN users u ON cr.created_by = u.user_id 
                LEFT JOIN users ao ON cr.arresting_officer_id = ao.user_id
                WHERE 1=1";
        
        $params = [];
        
        // Add search conditions
        if (!empty($search_term)) {
            switch ($search_type) {
                case 'case_number':
                    $sql .= " AND cr.case_number LIKE ?";
                    $params[] = "%$search_term%";
                    break;
                case 'suspect_name':
                    $sql .= " AND cr.suspect_name LIKE ?";
                    $params[] = "%$search_term%";
                    break;
                case 'crime_type':
                    $sql .= " AND cr.crime_type LIKE ?";
                    $params[] = "%$search_term%";
                    break;
                case 'location':
                    $sql .= " AND cr.incident_location LIKE ?";
                    $params[] = "%$search_term%";
                    break;
                default: // 'all'
                    $sql .= " AND (cr.case_number LIKE ? OR cr.suspect_name LIKE ? OR cr.crime_type LIKE ? OR cr.incident_location LIKE ?)";
                    $params = array_fill(0, 4, "%$search_term%");
                    break;
            }
        }
        
        // Add status filter
        if (!empty($status_filter)) {
            $sql .= " AND cr.case_status = ?";
            $params[] = $status_filter;
        }
        
        $sql .= " ORDER BY cr.updated_at DESC LIMIT 100";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $records = $stmt->fetchAll();
        
    } catch (PDOException $e) {
        $error_message = "Search failed. Please try again.";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Search Criminal Records - CRMS Officer</title>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Search Criminal Records</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Officer <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <!-- Search Form -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6 mb-8">
            <h2 class="text-white text-xl font-bold mb-4">Search Criminal Records</h2>
            
            <form method="GET" action="" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="md:col-span-2">
                        <label class="text-white text-sm font-semibold mb-2 block">Search Term</label>
                        <input type="text" name="search" placeholder="Enter search term..." 
                               value="<?php echo htmlspecialchars($search_term); ?>"
                               class="w-full px-4 py-2 bg-gray-700 text-white border border-gray-600 rounded focus:border-red-500 focus:outline-none">
                    </div>
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Search In</label>
                        <select name="search_type" class="w-full px-4 py-2 bg-gray-700 text-white border border-gray-600 rounded focus:border-red-500 focus:outline-none">
                            <option value="all" <?php echo $search_type == 'all' ? 'selected' : ''; ?>>All Fields</option>
                            <option value="case_number" <?php echo $search_type == 'case_number' ? 'selected' : ''; ?>>Case Number</option>
                            <option value="suspect_name" <?php echo $search_type == 'suspect_name' ? 'selected' : ''; ?>>Suspect Name</option>
                            <option value="crime_type" <?php echo $search_type == 'crime_type' ? 'selected' : ''; ?>>Crime Type</option>
                            <option value="location" <?php echo $search_type == 'location' ? 'selected' : ''; ?>>Location</option>
                        </select>
                    </div>
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Status Filter</label>
                        <select name="status_filter" class="w-full px-4 py-2 bg-gray-700 text-white border border-gray-600 rounded focus:border-red-500 focus:outline-none">
                            <option value="">All Statuses</option>
                            <option value="ongoing" <?php echo $status_filter == 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                            <option value="solved" <?php echo $status_filter == 'solved' ? 'selected' : ''; ?>>Solved</option>
                            <option value="paused" <?php echo $status_filter == 'paused' ? 'selected' : ''; ?>>Paused</option>
                            <option value="unresolved" <?php echo $status_filter == 'unresolved' ? 'selected' : ''; ?>>Unresolved</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex gap-4">
                    <button type="submit" class="bg-red-600 text-white px-6 py-2 rounded hover:bg-red-700 transition duration-300">
                        Search Records
                    </button>
                    <a href="search_records.php" class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700 transition duration-300">
                        Clear Search
                    </a>
                    <a href="dashboard.php" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 transition duration-300">
                        Back to Dashboard
                    </a>
                </div>
            </form>
        </div>

        <!-- Search Results -->
        <?php if (!empty($search_term)): ?>
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h2 class="text-white text-xl font-bold mb-4">
                    Search Results
                    <span class="text-sm text-gray-400">
                        (<?php echo count($records); ?> records found for "<?php echo htmlspecialchars($search_term); ?>")
                    </span>
                </h2>
                
                <?php if (!empty($records)): ?>
                    <div class="overflow-x-auto">
                        <table class="w-full text-white text-sm">
                            <thead>
                                <tr class="border-b border-gray-600">
                                    <th class="text-left py-3">Case Number</th>
                                    <th class="text-left py-3">Suspect</th>
                                    <th class="text-left py-3">Crime Type</th>
                                    <th class="text-left py-3">Status</th>
                                    <th class="text-left py-3">Location</th>
                                    <th class="text-left py-3">Incident Date</th>
                                    <th class="text-left py-3">Created By</th>
                                    <th class="text-left py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($records as $record): ?>
                                    <tr class="border-b border-gray-700 hover:bg-gray-700">
                                        <td class="py-3 font-mono"><?php echo htmlspecialchars($record['case_number']); ?></td>
                                        <td class="py-3"><?php echo htmlspecialchars($record['suspect_name']); ?></td>
                                        <td class="py-3"><?php echo htmlspecialchars($record['crime_type']); ?></td>
                                        <td class="py-3">
                                            <span class="px-2 py-1 rounded text-xs
                                                <?php 
                                                switch($record['case_status']) {
                                                    case 'solved': echo 'bg-green-600'; break;
                                                    case 'ongoing': echo 'bg-yellow-600'; break;
                                                    case 'paused': echo 'bg-blue-600'; break;
                                                    case 'unresolved': echo 'bg-red-600'; break;
                                                    default: echo 'bg-gray-600';
                                                }
                                                ?>">
                                                <?php echo ucfirst($record['case_status']); ?>
                                            </span>
                                        </td>
                                        <td class="py-3"><?php echo htmlspecialchars($record['incident_location']); ?></td>
                                        <td class="py-3"><?php echo date('M d, Y', strtotime($record['incident_date'])); ?></td>
                                        <td class="py-3"><?php echo htmlspecialchars($record['first_name'] . ' ' . $record['last_name']); ?></td>
                                        <td class="py-3">
                                            <div class="flex space-x-2">
                                                <a href="view_record.php?id=<?php echo $record['record_id']; ?>" 
                                                   class="text-blue-400 hover:text-blue-300 text-xs">View</a>
                                                <?php if ($record['created_by'] == $_SESSION['user_id']): ?>
                                                    <a href="edit_record.php?id=<?php echo $record['record_id']; ?>" 
                                                       class="text-green-400 hover:text-green-300 text-xs">Edit</a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Search Statistics -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-gray-700 rounded p-3 text-center">
                            <h4 class="text-yellow-400 font-semibold">Ongoing</h4>
                            <p class="text-white text-xl font-bold">
                                <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'ongoing'; })); ?>
                            </p>
                        </div>
                        <div class="bg-gray-700 rounded p-3 text-center">
                            <h4 class="text-green-400 font-semibold">Solved</h4>
                            <p class="text-white text-xl font-bold">
                                <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'solved'; })); ?>
                            </p>
                        </div>
                        <div class="bg-gray-700 rounded p-3 text-center">
                            <h4 class="text-blue-400 font-semibold">Paused</h4>
                            <p class="text-white text-xl font-bold">
                                <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'paused'; })); ?>
                            </p>
                        </div>
                        <div class="bg-gray-700 rounded p-3 text-center">
                            <h4 class="text-red-400 font-semibold">Unresolved</h4>
                            <p class="text-white text-xl font-bold">
                                <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'unresolved'; })); ?>
                            </p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <p class="text-gray-400 text-lg">No records found matching your search criteria.</p>
                        <p class="text-gray-500 text-sm mt-2">Try adjusting your search terms or filters.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Search Instructions -->
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h2 class="text-white text-xl font-bold mb-4">Search Instructions</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-red-400 font-semibold mb-3">Search Options</h3>
                        <ul class="text-gray-300 space-y-2 text-sm">
                            <li>• <strong>All Fields:</strong> Search across all record fields</li>
                            <li>• <strong>Case Number:</strong> Search by specific case number</li>
                            <li>• <strong>Suspect Name:</strong> Search by suspect's name</li>
                            <li>• <strong>Crime Type:</strong> Search by type of crime</li>
                            <li>• <strong>Location:</strong> Search by incident location</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-red-400 font-semibold mb-3">Search Tips</h3>
                        <ul class="text-gray-300 space-y-2 text-sm">
                            <li>• Use partial terms for broader results</li>
                            <li>• Combine search type and status filters</li>
                            <li>• Case numbers are in format: CASE-YYYY-NNNN</li>
                            <li>• Search is case-insensitive</li>
                            <li>• Results are limited to 100 records</li>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
