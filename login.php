<?php
     session_start();
     require_once 'config/database.php';
       

       $error_message = '';
       $success_message = '';

       if ($_SERVER['REQUEST_METHOD'] == 'POST') {
           $email = sanitizeInput($_POST['email']);
           $password = $_POST['password'];

           if (empty($email) || empty($password)) {
               $error_message = "Please fill in all required fields.";
           } else {
               try {
                   $pdo = getDBConnection();
                   $stmt = $pdo->prepare("SELECT user_id, first_name, last_name, email, password_hash, role, status FROM users WHERE email = ? AND status = 'active'");
                   $stmt->execute([$email]);
                   $user = $stmt->fetch();

                   if ($user && verifyPassword($password, $user['password_hash'])) {
                       $_SESSION['user_id'] = $user['user_id'];
                       $_SESSION['user_role'] = $user['role'];
                       $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                       $_SESSION['user_email'] = $user['email'];

                       // Redirect based on role
                       switch ($user['role']) {
                           case 'admin':
                               header('Location: admin/dashboard.php');
                               break;
                           case 'officer':
                               header('Location: officers/dashboard.php');
                               break;
                           case 'citizen':
                               header('Location: citizens/dashboard.php');
                               break;
                           default:
                               $error_message = "Invalid user role.";
                       }
                       exit();
                   } else {
                       $error_message = "Invalid email or password.";
                   }
               } catch (PDOException $e) {
                   $error_message = "Login failed. Please try again.";
               }
           }
       }

       include "back.php";
     ?>
<!DOCTYPE html>
<html>
    <head>
      <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
        <title>Login to CRMS</title>
        <style>
            .form-container {
                width: 500px;
                min-height: 600px;
                background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
                border: 2px solid #dc2626;
                box-shadow: 0px 0px 20px rgba(220, 38, 38, 0.3);
                border-radius: 12px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                padding: 40px;
                backdrop-filter: blur(10px);
            }

            .registration-form {
                display: flex;
                justify-content: center;
                flex-direction: column;
                gap: 20px;
            }

            .heading {
                color: white;
                font-size: 32px;
                text-align: center;
                margin-bottom: 30px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }

            .form-control {
                width: 100%;
                border: 2px solid #dc2626;
                border-radius: 8px;
                background: rgba(0,0,0,0.3);
                color: white;
                padding: 12px 16px;
                font-size: 16px;
                transition: all 0.3s ease;
            }

            .form-control:focus {
                outline: none;
                border-color: #ef4444;
                box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
                background: rgba(0,0,0,0.5);
            }

            .form-control::placeholder {
                color: #9ca3af;
            }

            .btn-primary {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                border: none;
                color: white;
                padding: 14px 24px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                text-transform: uppercase;
                letter-spacing: 1px;
            }

            .btn-primary:hover {
                background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
            }

            .center{
            margin-top:60px;
            margin-left:170px;
            }

            .form-control{
              width:350px;
              height:40px;
              border:2px dashed rgb(225, 3, 3);
              margin-left:47px

            }

            .submit-button{
              background-color: #dd0000;
              color:white;
              font-size:15px;
              width:200px;
              height:35px;
              border-radius:5px;
              align-content:center;
              margin-top:20px
            }

            .submit-button:hover{
              background-color:#dd0000;
              opacity:0.8;
              
            }
           
            /* .center-warning{
              display:flex;
              justify-content: center;
              flex-direction:column
            } */

            .warning{
              text-align:center;
               margin-top:10px;
            }

            .center-warning{
              
              width:30%;
              height:50%;
               margin-right:15%;
               margin-top:10%
            }

            .all{
              display:flex;
              justify-content: space-between;
              flex-direction:row;
              margin-top:2%
              
            }
            
            </style>
    </head>
    <body class="bg-gradient-to-l from-[#000000] via-[#121417]  to-[#25282d]">
      <div class="all">
      <div class="center">
        <div class="form-container">
            <div class="text-center mb-6">
                <img src="assets/police-force(3).png" alt="LPF Logo" class="h-16 w-16 mx-auto mb-4">
                <p class="heading">System Login</p>
                <p class="text-gray-300 text-sm">Access the Criminal Record Management System</p>
            </div>

            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded-lg mb-6 text-center">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <form id="form" class="registration-form" method="POST" action="">
                <div>
                    <label class="text-white text-sm font-semibold mb-2 block">Email Address</label>
                    <input type="email" name="email" class="form-control" placeholder="Enter your email address" required>
                </div>
                <div>
                    <label class="text-white text-sm font-semibold mb-2 block">Password</label>
                    <input type="password" name="password" class="form-control" placeholder="Enter your password" required>
                </div>
                <button type="submit" class="btn-primary w-full">
                    Login to System
                </button>

                <div class="text-center mt-6 space-y-3">
                    <p class="text-gray-400 text-sm">
                        Don't have an account?
                        <a href="register.php" class="text-red-400 hover:text-red-300 underline font-semibold">Register as Citizen</a>
                    </p>
                    <p class="text-gray-400 text-sm">
                        <a href="anonymous_tipoff.php" class="text-orange-400 hover:text-orange-300 underline">Submit Anonymous Tip-off</a>
                    </p>
                    <p class="text-gray-400 text-sm">
                        <a href="index.php" class="text-blue-400 hover:text-blue-300 underline">← Back to Home</a>
                    </p>
                </div>
            </form>
        </div>
      </div>
    

    <div class="center-warning">
     <div class="warning">
     <p class="text-3xl text-red-500 mb-5">
      CAUTION!
     </p>
     <p class="text-xl text-white mb-8">
      The users of this system are known by administrators and recorded in a secure database for quick authorization.
      Any unknown user or suspicious activity is captured almost instantly, and 2 more entry chances are given.
      If none succeed, the entries will be deemed malicious and the account/user/entries will be blocked.
     </p>
     <p class="text-sm text-red-500">To recover your account, visit the Lesotho Police Force Headquarters, or<br> <a class="underline" href="contact.php">go to contact page</a> to contact in-office workers</p>

     
     </div>
    </div>


      </div>
</html>