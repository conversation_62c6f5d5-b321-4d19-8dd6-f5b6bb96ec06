<?php
require_once '../config/database.php';
session_start();
requireRole(['admin']);

$message = '';
$error_message = '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = getDBConnection();
        
        if ($action == 'delete' && isset($_POST['user_id'])) {
            $user_id = (int)$_POST['user_id'];
            
            // Don't allow deleting the current admin
            if ($user_id == $_SESSION['user_id']) {
                $error_message = "You cannot delete your own account.";
            } else {
                $stmt = $pdo->prepare("UPDATE users SET status = 'inactive' WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $message = "User deactivated successfully.";
            }
        }
        
        if ($action == 'activate' && isset($_POST['user_id'])) {
            $user_id = (int)$_POST['user_id'];
            $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $message = "User activated successfully.";
        }
        
    } catch (PDOException $e) {
        $error_message = "Operation failed. Please try again.";
    }
}

// Get all users
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SELECT user_id, first_name, last_name, email, role, status, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll();
} catch (PDOException $e) {
    $error_message = "Failed to load users.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Manage Users - CRMS Admin</title>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Manage Users</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <!-- Messages -->
        <?php if (!empty($message)): ?>
            <div class="bg-green-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="mb-6">
            <a href="register_officer.php" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mr-4">
                Register New Officer
            </a>
            <a href="dashboard.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                Back to Dashboard
            </a>
        </div>

        <!-- Users Table -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
            <h2 class="text-white text-xl font-bold mb-4">All System Users</h2>
            
            <div class="overflow-x-auto">
                <table class="w-full text-white">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-3">ID</th>
                            <th class="text-left py-3">Name</th>
                            <th class="text-left py-3">Email</th>
                            <th class="text-left py-3">Role</th>
                            <th class="text-left py-3">Status</th>
                            <th class="text-left py-3">Registered</th>
                            <th class="text-left py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($users)): ?>
                            <?php foreach ($users as $user): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-700">
                                    <td class="py-3"><?php echo $user['user_id']; ?></td>
                                    <td class="py-3"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                                    <td class="py-3"><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td class="py-3">
                                        <span class="px-2 py-1 rounded text-xs
                                            <?php 
                                            switch($user['role']) {
                                                case 'admin': echo 'bg-red-600'; break;
                                                case 'officer': echo 'bg-blue-600'; break;
                                                case 'citizen': echo 'bg-green-600'; break;
                                                default: echo 'bg-gray-600';
                                            }
                                            ?>">
                                            <?php echo ucfirst($user['role']); ?>
                                        </span>
                                    </td>
                                    <td class="py-3">
                                        <span class="px-2 py-1 rounded text-xs
                                            <?php 
                                            switch($user['status']) {
                                                case 'active': echo 'bg-green-600'; break;
                                                case 'inactive': echo 'bg-red-600'; break;
                                                case 'suspended': echo 'bg-yellow-600'; break;
                                                default: echo 'bg-gray-600';
                                            }
                                            ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </td>
                                    <td class="py-3"><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                    <td class="py-3">
                                        <div class="flex space-x-2">
                                            <a href="edit_user.php?id=<?php echo $user['user_id']; ?>" 
                                               class="text-blue-400 hover:text-blue-300 text-sm">Edit</a>
                                            
                                            <?php if ($user['user_id'] != $_SESSION['user_id']): ?>
                                                <?php if ($user['status'] == 'active'): ?>
                                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to deactivate this user?')">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                                        <button type="submit" class="text-red-400 hover:text-red-300 text-sm">Deactivate</button>
                                                    </form>
                                                <?php else: ?>
                                                    <form method="POST" class="inline">
                                                        <input type="hidden" name="action" value="activate">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                                        <button type="submit" class="text-green-400 hover:text-green-300 text-sm">Activate</button>
                                                    </form>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-gray-500 text-sm">Current User</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4 text-gray-400">No users found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- User Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6 text-center">
                <h3 class="text-red-500 text-lg font-semibold mb-2">Total Admins</h3>
                <p class="text-white text-3xl font-bold">
                    <?php echo count(array_filter($users, function($u) { return $u['role'] == 'admin' && $u['status'] == 'active'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6 text-center">
                <h3 class="text-blue-500 text-lg font-semibold mb-2">Active Officers</h3>
                <p class="text-white text-3xl font-bold">
                    <?php echo count(array_filter($users, function($u) { return $u['role'] == 'officer' && $u['status'] == 'active'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6 text-center">
                <h3 class="text-green-500 text-lg font-semibold mb-2">Registered Citizens</h3>
                <p class="text-white text-3xl font-bold">
                    <?php echo count(array_filter($users, function($u) { return $u['role'] == 'citizen' && $u['status'] == 'active'; })); ?>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
