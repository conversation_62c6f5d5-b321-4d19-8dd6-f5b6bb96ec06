<?php
require_once '../config/database.php';
session_start();
requireRole(['citizen']);

$message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $address = sanitizeInput($_POST['address']);
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validation
    if (empty($first_name) || empty($last_name) || empty($email)) {
        $error_message = "Please fill in all required fields.";
    } elseif (!empty($new_password) && $new_password !== $confirm_password) {
        $error_message = "Passwords do not match.";
    } elseif (!empty($new_password) && strlen($new_password) < 6) {
        $error_message = "Password must be at least 6 characters long.";
    } else {
        try {
            $pdo = getDBConnection();
            
            // Check if email already exists for other users
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $_SESSION['user_id']]);
            if ($stmt->fetch()) {
                $error_message = "Email address already exists.";
            } else {
                // Update profile
                if (!empty($new_password)) {
                    // Update with new password
                    $password_hash = generateSecureHash($new_password);
                    $stmt = $pdo->prepare("
                        UPDATE users SET 
                            first_name = ?, last_name = ?, email = ?, phone = ?, address = ?, password_hash = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$first_name, $last_name, $email, $phone, $address, $password_hash, $_SESSION['user_id']]);
                } else {
                    // Update without changing password
                    $stmt = $pdo->prepare("
                        UPDATE users SET 
                            first_name = ?, last_name = ?, email = ?, phone = ?, address = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$first_name, $last_name, $email, $phone, $address, $_SESSION['user_id']]);
                }
                
                // Update session data
                $_SESSION['user_name'] = $first_name . ' ' . $last_name;
                $_SESSION['user_email'] = $email;
                
                $message = "Profile updated successfully!";
            }
        } catch (PDOException $e) {
            $error_message = "Failed to update profile. Please try again.";
        }
    }
}

// Get current user data
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
} catch (PDOException $e) {
    $error_message = "Failed to load profile data.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>My Profile - CRMS Citizen</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 30px;
        }
        .form-control {
            width: 100%;
            height: 45px;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 0 15px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - My Profile</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="bg-green-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="form-container">
                <h2 class="text-white text-2xl font-bold text-center mb-6">My Profile</h2>
                
                <form method="POST" action="" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">First Name *</label>
                            <input type="text" name="first_name" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                        </div>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Last Name *</label>
                            <input type="text" name="last_name" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                        </div>
                    </div>

                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Email Address *</label>
                        <input type="email" name="email" class="form-control" 
                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Phone Number</label>
                            <input type="tel" name="phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Address</label>
                            <input type="text" name="address" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>">
                        </div>
                    </div>

                    <!-- Password Section -->
                    <div class="bg-gray-700 rounded-lg p-4 mt-6">
                        <h3 class="text-white text-lg font-semibold mb-4">Change Password (Optional)</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">New Password</label>
                                <input type="password" name="new_password" class="form-control" 
                                       placeholder="Leave blank to keep current password">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Confirm New Password</label>
                                <input type="password" name="confirm_password" class="form-control" 
                                       placeholder="Confirm new password">
                            </div>
                        </div>
                        <p class="text-gray-400 text-xs mt-2">Password must be at least 6 characters long</p>
                    </div>

                    <!-- Account Information -->
                    <div class="bg-gray-700 rounded-lg p-4 mt-6">
                        <h3 class="text-white text-lg font-semibold mb-4">Account Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <p class="text-gray-300">User ID: <span class="text-white"><?php echo $user['user_id']; ?></span></p>
                                <p class="text-gray-300">Account Type: <span class="text-white">Citizen</span></p>
                            </div>
                            <div>
                                <p class="text-gray-300">Member Since: <span class="text-white"><?php echo date('M d, Y', strtotime($user['created_at'])); ?></span></p>
                                <p class="text-gray-300">Last Updated: <span class="text-white"><?php echo date('M d, Y H:i', strtotime($user['updated_at'])); ?></span></p>
                            </div>
                        </div>
                    </div>

                    <div class="text-center pt-6">
                        <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300 mr-4">
                            Update Profile
                        </button>
                        <a href="dashboard.php" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                            Cancel
                        </a>
                    </div>
                </form>

                <div class="text-center mt-6">
                    <a href="dashboard.php" class="text-blue-400 hover:text-blue-300 underline">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
