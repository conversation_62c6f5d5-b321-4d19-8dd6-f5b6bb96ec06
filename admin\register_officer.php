<?php
require_once '../config/database.php';
session_start();
requireRole(['admin']);

$message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $email = sanitizeInput($_POST['email']);
    $service = sanitizeInput($_POST['service']);
    $phone = sanitizeInput($_POST['phone']);
    $address = sanitizeInput($_POST['address']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validation
    if (empty($first_name) || empty($last_name) || empty($email) || empty($service) || empty($password)) {
        $error_message = "Please fill in all required fields.";
    } elseif ($password !== $confirm_password) {
        $error_message = "Passwords do not match.";
    } elseif (strlen($password) < 6) {
        $error_message = "Password must be at least 6 characters long.";
    } else {
        try {
            $pdo = getDBConnection();
            
            // Check if email already exists
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error_message = "Email address already registered.";
            } else {
                // Insert new officer
                $password_hash = generateSecureHash($password);
                $stmt = $pdo->prepare("
                    INSERT INTO users (first_name, last_name, email, password_hash, role, service, phone, address) 
                    VALUES (?, ?, ?, ?, 'officer', ?, ?, ?)
                ");
                $stmt->execute([$first_name, $last_name, $email, $password_hash, $service, $phone, $address]);
                
                $message = "Police officer registered successfully!";
                
                // Clear form data
                $first_name = $last_name = $email = $service = $phone = $address = '';
            }
        } catch (PDOException $e) {
            $error_message = "Registration failed. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Register Officer - CRMS Admin</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 30px;
        }
        .form-control {
            width: 100%;
            height: 45px;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 0 15px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Register Police Officer</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <a href="manage_users.php" class="text-white hover:text-gray-300">Manage Users</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="bg-green-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="form-container">
                <h2 class="text-white text-2xl font-bold text-center mb-6">Register New Police Officer</h2>
                
                <form method="POST" action="" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">First Name *</label>
                            <input type="text" name="first_name" class="form-control" 
                                   placeholder="Enter first name" 
                                   value="<?php echo htmlspecialchars($first_name ?? ''); ?>" required>
                        </div>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Last Name *</label>
                            <input type="text" name="last_name" class="form-control" 
                                   placeholder="Enter last name" 
                                   value="<?php echo htmlspecialchars($last_name ?? ''); ?>" required>
                        </div>
                    </div>

                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Email Address *</label>
                        <input type="email" name="email" class="form-control" 
                               placeholder="Enter email address" 
                               value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                    </div>

                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Service/Department *</label>
                        <select name="service" class="form-control" required>
                            <option value="">Select Service</option>
                            <option value="Criminal Investigation">Criminal Investigation</option>
                            <option value="Traffic Police">Traffic Police</option>
                            <option value="Community Policing">Community Policing</option>
                            <option value="Special Operations">Special Operations</option>
                            <option value="Forensics">Forensics</option>
                            <option value="Administration">Administration</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Phone Number</label>
                            <input type="tel" name="phone" class="form-control" 
                                   placeholder="Enter phone number" 
                                   value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                        </div>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Address</label>
                            <input type="text" name="address" class="form-control" 
                                   placeholder="Enter address" 
                                   value="<?php echo htmlspecialchars($address ?? ''); ?>">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Password *</label>
                            <input type="password" name="password" class="form-control" 
                                   placeholder="Enter password" required>
                        </div>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Confirm Password *</label>
                            <input type="password" name="confirm_password" class="form-control" 
                                   placeholder="Confirm password" required>
                        </div>
                    </div>

                    <div class="text-center pt-4">
                        <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300">
                            Register Officer
                        </button>
                    </div>
                </form>

                <div class="text-center mt-6">
                    <a href="manage_users.php" class="text-blue-400 hover:text-blue-300 underline">
                        View All Users
                    </a>
                    <span class="text-gray-400 mx-2">|</span>
                    <a href="dashboard.php" class="text-blue-400 hover:text-blue-300 underline">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
