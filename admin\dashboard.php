<?php

require_once '../config/database.php';
session_start();
requireRole(['admin']);

// Get dashboard statistics
try {
    $pdo = getDBConnection();
    
    // Get case statistics
    $stmt = $pdo->query("SELECT 
        COUNT(*) as total_cases,
        SUM(CASE WHEN case_status = 'solved' THEN 1 ELSE 0 END) as solved_cases,
        SUM(CASE WHEN case_status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_cases,
        SUM(CASE WHEN case_status = 'paused' THEN 1 ELSE 0 END) as paused_cases,
        SUM(CASE WHEN case_status = 'unresolved' THEN 1 ELSE 0 END) as unresolved_cases
        FROM criminal_records");
    $case_stats = $stmt->fetch();
    
    // Get user statistics
    $stmt = $pdo->query("SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'officer' THEN 1 ELSE 0 END) as total_officers,
        SUM(CASE WHEN role = 'citizen' THEN 1 ELSE 0 END) as total_citizens
        FROM users WHERE status = 'active'");
    $user_stats = $stmt->fetch();
    
    // Get recent cases
    $stmt = $pdo->query("SELECT cr.*, u.first_name, u.last_name 
        FROM criminal_records cr 
        LEFT JOIN users u ON cr.created_by = u.user_id 
        ORDER BY cr.created_at DESC LIMIT 5");
    $recent_cases = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = "Failed to load dashboard data.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Admin Dashboard - CRMS</title>
    <style>
        .stat-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
        }
        .nav-link {
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-link:hover {
            background-color: #dc2626;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Administrator Dashboard</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Quick Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="manage_users.php" class="nav-link bg-gray-700">Manage Users</a>
                <a href="manage_records.php" class="nav-link bg-gray-700">Criminal Records</a>
                <a href="register_officer.php" class="nav-link bg-gray-700">Register Officer</a>
                <a href="reports.php" class="nav-link bg-gray-700">Generate Reports</a>
                <a href="system_settings.php" class="nav-link bg-gray-700">System Settings</a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card">
                <h3 class="text-red-500 text-lg font-semibold mb-2">Total Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['total_cases'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-green-500 text-lg font-semibold mb-2">Solved Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['solved_cases'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-yellow-500 text-lg font-semibold mb-2">Ongoing Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['ongoing_cases'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-blue-500 text-lg font-semibold mb-2">Paused Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['paused_cases'] ?? 0; ?></p>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="stat-card">
                <h3 class="text-red-500 text-lg font-semibold mb-2">Unresolved Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['unresolved_cases'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-purple-500 text-lg font-semibold mb-2">Police Officers</h3>
                <p class="text-white text-3xl font-bold"><?php echo $user_stats['total_officers'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-cyan-500 text-lg font-semibold mb-2">Registered Citizens</h3>
                <p class="text-white text-3xl font-bold"><?php echo $user_stats['total_citizens'] ?? 0; ?></p>
            </div>
        </div>

        <!-- Recent Cases -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
            <h2 class="text-white text-xl font-bold mb-4">Recent Cases</h2>
            <div class="overflow-x-auto">
                <table class="w-full text-white">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-2">Case Number</th>
                            <th class="text-left py-2">Suspect Name</th>
                            <th class="text-left py-2">Crime Type</th>
                            <th class="text-left py-2">Status</th>
                            <th class="text-left py-2">Created By</th>
                            <th class="text-left py-2">Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($recent_cases)): ?>
                            <?php foreach ($recent_cases as $case): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-700">
                                    <td class="py-2"><?php echo htmlspecialchars($case['case_number']); ?></td>
                                    <td class="py-2"><?php echo htmlspecialchars($case['suspect_name']); ?></td>
                                    <td class="py-2"><?php echo htmlspecialchars($case['crime_type']); ?></td>
                                    <td class="py-2">
                                        <span class="px-2 py-1 rounded text-xs
                                            <?php 
                                            switch($case['case_status']) {
                                                case 'solved': echo 'bg-green-600'; break;
                                                case 'ongoing': echo 'bg-yellow-600'; break;
                                                case 'paused': echo 'bg-blue-600'; break;
                                                case 'unresolved': echo 'bg-red-600'; break;
                                                default: echo 'bg-gray-600';
                                            }
                                            ?>">
                                            <?php echo ucfirst($case['case_status']); ?>
                                        </span>
                                    </td>
                                    <td class="py-2"><?php echo htmlspecialchars($case['first_name'] . ' ' . $case['last_name']); ?></td>
                                    <td class="py-2"><?php echo date('M d, Y', strtotime($case['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4 text-gray-400">No cases found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
