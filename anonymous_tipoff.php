<?php
require_once 'config/database.php';

$message = '';
$error_message = '';

// Handle tip-off submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $tip_type = trim($_POST['tip_type'] ?? '');
    $tip_description = trim($_POST['tip_description'] ?? '');
    $incident_date = $_POST['incident_date'] ?? '';
    $incident_location = trim($_POST['incident_location'] ?? '');
    $suspect_info = trim($_POST['suspect_info'] ?? '');
    $reporter_type = $_POST['reporter_type'] ?? 'anonymous';
    
    // Validation
    if (empty($tip_type) || empty($tip_description)) {
        $error_message = "Please fill in all required fields.";
    } else {
        try {
            $pdo = getDBConnection();
            
            // Generate unique tip number
            $tip_number = 'TIP-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Check if tip number exists
            $stmt = $pdo->prepare("SELECT tip_number FROM tip_offs WHERE tip_number = ?");
            $stmt->execute([$tip_number]);
            while ($stmt->fetch()) {
                $tip_number = 'TIP-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $stmt->execute([$tip_number]);
            }
            
            // Insert new tip-off
            $stmt = $pdo->prepare("
                INSERT INTO tip_offs (
                    tip_number, reporter_type, tip_type, tip_description,
                    incident_date, incident_location, suspect_info, case_visibility, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'public', 'new')
            ");
            
            $stmt->execute([
                $tip_number, $reporter_type, $tip_type, $tip_description,
                $incident_date ?: null, $incident_location, $suspect_info
            ]);
            
            // Add initial update
            $stmt = $pdo->prepare("
                INSERT INTO tip_off_updates (tip_number, update_type, update_description, updated_by) 
                VALUES (?, 'status_change', 'Tip-off submitted anonymously', 1)
            ");
            $stmt->execute([$tip_number]);
            
            $message = "Tip-off submitted successfully! Reference Number: " . $tip_number . ". Thank you for helping keep our community safe.";
            
            // Clear form data
            $tip_type = $tip_description = $incident_date = $incident_location = $suspect_info = '';
            
        } catch (PDOException $e) {
            $error_message = "Failed to submit tip-off. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <link href="index.css" rel="stylesheet">
    <title>Anonymous Tip-off - CRMS</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 8px 12px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
        }
        .hero-section {
            background: linear-gradient(135deg, rgba(0,0,0,0.8), rgba(18,20,23,0.9)),
                       url('./assets/cybersecurity4.jpg') center/cover;
            min-height: 40vh;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Anonymous Tip-off</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.php" class="text-white hover:text-gray-300">Home</a>
                <a href="login.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Login</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section flex items-center justify-center">
        <div class="text-center">
            <h1 class="text-white text-4xl font-bold mb-4">Submit Anonymous Tip-off</h1>
            <p class="text-gray-300 text-lg">Help us keep the community safe by reporting suspicious activities</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="form-container">
                <div class="mb-6">
                    <h2 class="text-white text-2xl font-bold mb-4">Submit a Tip-off</h2>
                    <div class="bg-blue-900 border border-blue-600 rounded-lg p-4 mb-6">
                        <h3 class="text-blue-300 font-semibold mb-2">Important Information:</h3>
                        <ul class="text-blue-200 text-sm space-y-1">
                            <li>• Your identity will remain completely anonymous</li>
                            <li>• You will receive a reference number to track your tip-off</li>
                            <li>• Provide as much detail as possible to help our investigation</li>
                            <li>• False reports are a criminal offense</li>
                        </ul>
                    </div>
                </div>
                
                <?php if (!empty($error_message)): ?>
                    <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($message)): ?>
                    <div class="bg-green-600 text-white p-4 rounded-lg mb-6">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" class="space-y-6">
                    <!-- Reporter Type -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Reporter Information</h3>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Reporting As</label>
                            <div class="space-y-2">
                                <label class="checkbox-label">
                                    <input type="radio" name="reporter_type" value="anonymous" checked class="mr-2">
                                    Anonymous (Recommended for safety)
                                </label>
                                <label class="checkbox-label">
                                    <input type="radio" name="reporter_type" value="registered" class="mr-2">
                                    Registered Citizen (Login required after submission)
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Tip Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Tip Information</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Type of Tip *</label>
                                <select name="tip_type" class="form-control" required>
                                    <option value="">Select Tip Type</option>
                                    <option value="Drug Activity">Drug Activity</option>
                                    <option value="Theft/Burglary">Theft/Burglary</option>
                                    <option value="Assault/Violence">Assault/Violence</option>
                                    <option value="Fraud/Scam">Fraud/Scam</option>
                                    <option value="Traffic Violation">Traffic Violation</option>
                                    <option value="Domestic Violence">Domestic Violence</option>
                                    <option value="Vandalism">Vandalism</option>
                                    <option value="Suspicious Activity">Suspicious Activity</option>
                                    <option value="Weapon Related">Weapon Related</option>
                                    <option value="Other Criminal Activity">Other Criminal Activity</option>
                                </select>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Detailed Description *</label>
                                <textarea name="tip_description" class="form-control" rows="6" 
                                          placeholder="Provide as much detail as possible about what you witnessed or know. Include dates, times, locations, people involved, etc."
                                          required><?php echo htmlspecialchars($tip_description ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Incident Details -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Incident Details (Optional)</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Incident Date</label>
                                <input type="date" name="incident_date" class="form-control" 
                                       value="<?php echo htmlspecialchars($incident_date ?? ''); ?>">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Location</label>
                                <input type="text" name="incident_location" class="form-control" 
                                       placeholder="Where did this occur?"
                                       value="<?php echo htmlspecialchars($incident_location ?? ''); ?>">
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="text-white text-sm font-semibold mb-2 block">Suspect Information</label>
                            <textarea name="suspect_info" class="form-control" rows="3"
                                      placeholder="Describe any suspects involved (appearance, names, vehicles, etc.)"><?php echo htmlspecialchars($suspect_info ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Disclaimer -->
                    <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4">
                        <h3 class="text-yellow-300 font-semibold mb-2">Legal Disclaimer:</h3>
                        <p class="text-yellow-200 text-sm">
                            By submitting this tip-off, you confirm that the information provided is true to the best of your knowledge. 
                            Providing false information to law enforcement is a criminal offense. Your tip will be investigated by 
                            qualified officers and may be used in legal proceedings.
                        </p>
                    </div>

                    <div class="text-center pt-4">
                        <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300 mr-4">
                            Submit Tip-off
                        </button>
                        <a href="index.php" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 border-t-2 border-red-600 p-4 mt-8">
        <div class="text-center text-gray-400">
            <p>&copy; 2024 Lesotho Police Force - Criminal Record Management System</p>
            <p class="text-sm mt-2">Your safety and anonymity are our priority</p>
        </div>
    </footer>
</body>
</html>
