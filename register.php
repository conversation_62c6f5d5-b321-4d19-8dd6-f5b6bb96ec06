<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Register - CRMS</title>
    <style>
        .form-container {
            width: 600px;
            min-height: auto;
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            box-shadow: 0px 0px 20px rgba(220, 38, 38, 0.3);
            border-radius: 12px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            padding: 40px;
            backdrop-filter: blur(10px);
        }

        .registration-form {
            display: flex;
            justify-content: center;
            flex-direction: column;
            gap: 20px;
        }

        .heading {
            color: white;
            font-size: 32px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 8px;
            background: rgba(0,0,0,0.3);
            color: white;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
            background: rgba(0,0,0,0.5);
        }

        .form-control::placeholder {
            color: #9ca3af;
        }

        .btn-primary {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            border: none;
            color: white;
            padding: 14px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
        }

        .center {
            margin-top: 60px;
            margin-left: auto;
            margin-right: auto;
        }

        .form-control {
            width: 100%;
            height: 40px;
            border: 2px dashed rgb(225, 3, 3);
        }

        .submit-button {
            background-color: #dd0000;
            color: white;
            font-size: 15px;
            width: 200px;
            height: 35px;
            border-radius: 5px;
            align-content: center;
            margin-top: 20px;
            margin-left: auto;
            margin-right: auto;
        }

        .submit-button:hover {
            background-color: #dd0000;
            opacity: 0.8;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d]">
    <?php
    require_once 'config/database.php';
    session_start();
    
    $error_message = '';
    $success_message = '';
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $first_name = sanitizeInput($_POST['first_name']);
        $last_name = sanitizeInput($_POST['last_name']);
        $email = sanitizeInput($_POST['email']);
        $phone = sanitizeInput($_POST['phone']);
        $address = sanitizeInput($_POST['address']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        
        // Validation
        if (empty($first_name) || empty($last_name) || empty($email) || empty($password)) {
            $error_message = "Please fill in all required fields.";
        } elseif ($password !== $confirm_password) {
            $error_message = "Passwords do not match.";
        } elseif (strlen($password) < 6) {
            $error_message = "Password must be at least 6 characters long.";
        } else {
            try {
                $pdo = getDBConnection();
                
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    $error_message = "Email address already registered.";
                } else {
                    // Insert new citizen
                    $password_hash = generateSecureHash($password);
                    $stmt = $pdo->prepare("
                        INSERT INTO users (first_name, last_name, email, password_hash, role, phone, address) 
                        VALUES (?, ?, ?, ?, 'citizen', ?, ?)
                    ");
                    $stmt->execute([$first_name, $last_name, $email, $password_hash, $phone, $address]);
                    
                    $success_message = "Registration successful! You can now login.";
                }
            } catch (PDOException $e) {
                $error_message = "Registration failed. Please try again.";
            }
        }
    }
    ?>

    <div class="center">
        <div class="form-container">
            <div class="text-center mb-6">
                <img src="assets/police-force(3).png" alt="LPF Logo" class="h-16 w-16 mx-auto mb-4">
                <p class="heading">Citizen Registration</p>
                <p class="text-gray-300 text-sm">Join the Criminal Record Management System</p>
            </div>

            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded-lg mb-6 text-center">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_message)): ?>
                <div class="bg-green-600 text-white p-4 rounded-lg mb-6 text-center">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form class="registration-form" method="POST" action="">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">First Name *</label>
                        <input type="text" name="first_name" class="form-control" placeholder="Enter your first name"
                               value="<?php echo htmlspecialchars($first_name ?? ''); ?>" required>
                    </div>
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Last Name *</label>
                        <input type="text" name="last_name" class="form-control" placeholder="Enter your last name"
                               value="<?php echo htmlspecialchars($last_name ?? ''); ?>" required>
                    </div>
                </div>

                <div>
                    <label class="text-white text-sm font-semibold mb-2 block">Email Address *</label>
                    <input type="email" name="email" class="form-control" placeholder="Enter your email address"
                           value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Password *</label>
                        <input type="password" name="password" class="form-control" placeholder="Create a password" required>
                    </div>
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Confirm Password *</label>
                        <input type="password" name="confirm_password" class="form-control" placeholder="Confirm your password" required>
                    </div>
                </div>

                <div>
                    <label class="text-white text-sm font-semibold mb-2 block">Phone Number</label>
                    <input type="text" name="phone" class="form-control" placeholder="Enter your phone number (optional)"
                           value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                </div>

                <div>
                    <label class="text-white text-sm font-semibold mb-2 block">Address</label>
                    <textarea name="address" class="form-control" rows="3" placeholder="Enter your address (optional)"><?php echo htmlspecialchars($address ?? ''); ?></textarea>
                </div>

                <button type="submit" class="btn-primary w-full">
                    Register Account
                </button>

                <div class="text-center mt-6 space-y-3">
                    <p class="text-gray-400 text-sm">
                        Already have an account?
                        <a href="login.php" class="text-red-400 hover:text-red-300 underline font-semibold">Login here</a>
                    </p>
                    <p class="text-gray-400 text-sm">
                        <a href="anonymous_tipoff.php" class="text-orange-400 hover:text-orange-300 underline">Submit Anonymous Tip-off</a>
                    </p>
                    <p class="text-gray-400 text-sm">
                        <a href="index.php" class="text-blue-400 hover:text-blue-300 underline">← Back to Home</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <?php include "back.php"; ?>
</body>
</html>
