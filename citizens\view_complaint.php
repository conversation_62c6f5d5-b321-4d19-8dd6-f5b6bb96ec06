<?php
require_once '../config/database.php';
session_start();
requireRole(['citizen']);

$complaint = null;
$error_message = '';

if (isset($_GET['id'])) {
    $complaint_id = (int)$_GET['id'];
    
    try {
        $pdo = getDBConnection();
        
        // Get complaint details (only complaints filed by this citizen)
        $stmt = $pdo->prepare("SELECT c.*, 
            u.first_name as officer_fname, u.last_name as officer_lname, u.email as officer_email
            FROM complaints c 
            LEFT JOIN users u ON c.assigned_officer_id = u.user_id 
            WHERE c.complaint_id = ? AND c.citizen_id = ?");
        $stmt->execute([$complaint_id, $_SESSION['user_id']]);
        $complaint = $stmt->fetch();
        
    } catch (PDOException $e) {
        $error_message = "Failed to load complaint details.";
    }
} else {
    $error_message = "No complaint ID provided.";
}

if (!$complaint) {
    $error_message = "Complaint not found or you don't have permission to view it.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>View Complaint - CRMS Citizen</title>
    <style>
        .info-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        @media print {
            .no-print { display: none; }
            body { background: white !important; }
            .bg-gray-800 { background: white !important; color: black !important; }
            .text-white { color: black !important; }
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4 no-print">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - View Complaint</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="my_complaints.php" class="text-white hover:text-gray-300">Back to My Complaints</a>
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($error_message); ?>
                <div class="mt-4">
                    <a href="my_complaints.php" class="underline">Return to My Complaints</a>
                </div>
            </div>
        <?php else: ?>
            <!-- Action Buttons -->
            <div class="mb-6 flex gap-4 no-print">
                <button onclick="window.print()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Print Complaint</button>
                <a href="my_complaints.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">Back to My Complaints</a>
                <a href="new_complaint.php" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">File New Complaint</a>
            </div>

            <!-- Complaint Header -->
            <div class="info-card mb-6">
                <div class="text-center mb-6">
                    <h1 class="text-white text-3xl font-bold">Complaint Details</h1>
                    <p class="text-gray-300">Complaint Number: <span class="font-mono text-red-400"><?php echo htmlspecialchars($complaint['complaint_number']); ?></span></p>
                    <p class="text-gray-300">Generated on: <?php echo date('F d, Y H:i'); ?></p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Complaint Status -->
                    <div>
                        <h3 class="text-white text-lg font-semibold mb-2">Complaint Status</h3>
                        <span class="px-4 py-2 rounded text-sm font-semibold
                            <?php 
                            switch($complaint['status']) {
                                case 'resolved': echo 'bg-green-600 text-white'; break;
                                case 'attended': echo 'bg-blue-600 text-white'; break;
                                case 'in_progress': echo 'bg-yellow-600 text-white'; break;
                                case 'not_attended': echo 'bg-red-600 text-white'; break;
                                default: echo 'bg-gray-600 text-white';
                            }
                            ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $complaint['status'])); ?>
                        </span>
                    </div>
                    
                    <!-- Dates -->
                    <div>
                        <h3 class="text-white text-lg font-semibold mb-2">Important Dates</h3>
                        <p class="text-gray-300">Incident Date: <span class="text-white"><?php echo date('F d, Y', strtotime($complaint['incident_date'])); ?></span></p>
                        <p class="text-gray-300">Complaint Filed: <span class="text-white"><?php echo date('F d, Y H:i', strtotime($complaint['created_at'])); ?></span></p>
                        <p class="text-gray-300">Last Updated: <span class="text-white"><?php echo date('F d, Y H:i', strtotime($complaint['updated_at'])); ?></span></p>
                    </div>
                </div>
            </div>

            <!-- Complaint Information -->
            <div class="info-card mb-6">
                <h2 class="text-white text-xl font-bold mb-4">Complaint Information</h2>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-gray-300">Complaint Type: <span class="text-white font-semibold"><?php echo htmlspecialchars($complaint['complaint_type']); ?></span></p>
                        </div>
                        <div>
                            <p class="text-gray-300">Incident Location: <span class="text-white"><?php echo htmlspecialchars($complaint['incident_location']); ?></span></p>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-white font-semibold mb-2">Complaint Description:</h3>
                        <p class="text-gray-300 bg-gray-700 p-4 rounded"><?php echo nl2br(htmlspecialchars($complaint['complaint_description'])); ?></p>
                    </div>
                </div>
            </div>

            <!-- Officer Assignment -->
            <div class="info-card mb-6">
                <h2 class="text-white text-xl font-bold mb-4">Officer Assignment</h2>
                <?php if ($complaint['officer_fname']): ?>
                    <div class="bg-green-900 border border-green-600 rounded p-4">
                        <p class="text-green-300 font-semibold mb-2">✓ Officer Assigned</p>
                        <p class="text-gray-300">Assigned Officer: <span class="text-white"><?php echo htmlspecialchars($complaint['officer_fname'] . ' ' . $complaint['officer_lname']); ?></span></p>
                        <p class="text-gray-300">Contact Email: <span class="text-white"><?php echo htmlspecialchars($complaint['officer_email']); ?></span></p>
                    </div>
                <?php else: ?>
                    <div class="bg-yellow-900 border border-yellow-600 rounded p-4">
                        <p class="text-yellow-300 font-semibold mb-2">⏳ Awaiting Assignment</p>
                        <p class="text-gray-300">Your complaint is in the queue and will be assigned to an officer soon. You will be notified once an officer is assigned.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Response -->
            <?php if ($complaint['response']): ?>
                <div class="info-card mb-6">
                    <h2 class="text-white text-xl font-bold mb-4">Official Response</h2>
                    <div class="bg-blue-900 border border-blue-600 rounded p-4">
                        <p class="text-gray-300"><?php echo nl2br(htmlspecialchars($complaint['response'])); ?></p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Status Guide -->
            <div class="info-card">
                <h2 class="text-white text-xl font-bold mb-4">Status Guide</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <span class="px-2 py-1 bg-red-600 text-white rounded text-xs">Not Attended</span>
                            <p class="text-gray-300">Your complaint has been received and is waiting for review.</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="px-2 py-1 bg-blue-600 text-white rounded text-xs">Attended</span>
                            <p class="text-gray-300">An officer has been assigned and initial review has begun.</p>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <span class="px-2 py-1 bg-yellow-600 text-white rounded text-xs">In Progress</span>
                            <p class="text-gray-300">Your complaint is being actively investigated.</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="px-2 py-1 bg-green-600 text-white rounded text-xs">Resolved</span>
                            <p class="text-gray-300">Your complaint has been resolved. Check the response above.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="mt-6 bg-gray-800 border border-gray-600 rounded-lg p-4">
                <h3 class="text-white font-bold mb-2">Need Help?</h3>
                <p class="text-gray-300 text-sm">
                    If you have questions about your complaint or need to provide additional information, 
                    please contact the Lesotho Police Force at:
                </p>
                <div class="mt-2 text-gray-300 text-sm">
                    <p>📞 Emergency: 911</p>
                    <p>📞 Non-Emergency: +266 2231-2345</p>
                    <p>📧 Email: <EMAIL></p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
