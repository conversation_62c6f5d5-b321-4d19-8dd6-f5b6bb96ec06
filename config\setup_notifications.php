<?php
/**
 * Database Setup Script for Notifications System
 * Run this file to add notification functionality to CRMS
 */

require_once 'database.php';

try {
    $pdo = getDBConnection();
    
    // Create complaint_notifications table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS complaint_notifications (
            notification_id INT AUTO_INCREMENT PRIMARY KEY,
            complaint_number VARCHAR(50) NOT NULL,
            admin_id INT NOT NULL,
            notification_type ENUM('new_complaint', 'status_update', 'assignment') NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    ");
    
    // Create tip_off_notifications table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tip_off_notifications (
            notification_id INT AUTO_INCREMENT PRIMARY KEY,
            tip_number VARCHAR(50) NOT NULL,
            admin_id INT NOT NULL,
            notification_type ENUM('new_tipoff', 'status_update', 'assignment') NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    ");
    
    echo "Notifications system setup completed successfully!<br>";
    echo "New tables created:<br>";
    echo "- complaint_notifications<br>";
    echo "- tip_off_notifications<br>";
    echo "<br><a href='../index.php'>Go to Home Page</a>";
    
} catch (PDOException $e) {
    die("Notifications setup failed: " . $e->getMessage());
}
?>
