<?php
require_once '../config/database.php';
session_start();
requireRole(['citizen']);

$message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $complaint_type = sanitizeInput($_POST['complaint_type']);
    $complaint_description = sanitizeInput($_POST['complaint_description']);
    $incident_date = $_POST['incident_date'];
    $incident_location = sanitizeInput($_POST['incident_location']);
    
    // Validation
    if (empty($complaint_type) || empty($complaint_description) || empty($incident_date) || empty($incident_location)) {
        $error_message = "Please fill in all required fields.";
    } else {
        try {
            $pdo = getDBConnection();
            
            // Generate unique complaint number
            $complaint_number = 'COMP-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Check if complaint number exists
            $stmt = $pdo->prepare("SELECT complaint_number FROM complaints WHERE complaint_number = ?");
            $stmt->execute([$complaint_number]);
            while ($stmt->fetch()) {
                $complaint_number = 'COMP-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $stmt->execute([$complaint_number]);
            }
            
            // Insert new complaint
            $stmt = $pdo->prepare("
                INSERT INTO complaints (
                    complaint_number, citizen_id, complaint_type, complaint_description,
                    incident_date, incident_location, status
                ) VALUES (?, ?, ?, ?, ?, ?, 'not_attended')
            ");
            
            $stmt->execute([
                $complaint_number, $_SESSION['user_id'], $complaint_type, $complaint_description,
                $incident_date, $incident_location
            ]);
            
            $message = "Complaint filed successfully! Complaint Number: " . $complaint_number . ". You will be contacted within 24-48 hours.";
            
            // Clear form data
            $complaint_type = $complaint_description = $incident_date = $incident_location = '';
            
        } catch (PDOException $e) {
            $error_message = "Failed to file complaint. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>File Complaint - CRMS Citizen</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 30px;
        }
        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 10px 15px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
        }
        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - File Complaint</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <a href="my_complaints.php" class="text-white hover:text-gray-300">My Complaints</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto">
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="bg-green-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Important Notice -->
            <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4 mb-6">
                <h3 class="text-yellow-300 font-bold mb-2">⚠️ Important Information</h3>
                <ul class="text-yellow-200 text-sm space-y-1">
                    <li>• For emergencies, call 911 immediately</li>
                    <li>• Complaints are reviewed within 24-48 hours</li>
                    <li>• You will receive updates on your complaint status</li>
                    <li>• Provide as much detail as possible for faster processing</li>
                </ul>
            </div>

            <div class="form-container">
                <h2 class="text-white text-2xl font-bold text-center mb-6">File a Complaint</h2>
                
                <form method="POST" action="" class="space-y-6">
                    <!-- Complaint Type -->
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Complaint Type *</label>
                        <select name="complaint_type" class="form-control" required>
                            <option value="">Select Complaint Type</option>
                            <option value="Theft" <?php echo ($complaint_type ?? '') == 'Theft' ? 'selected' : ''; ?>>Theft</option>
                            <option value="Assault" <?php echo ($complaint_type ?? '') == 'Assault' ? 'selected' : ''; ?>>Assault</option>
                            <option value="Harassment" <?php echo ($complaint_type ?? '') == 'Harassment' ? 'selected' : ''; ?>>Harassment</option>
                            <option value="Vandalism" <?php echo ($complaint_type ?? '') == 'Vandalism' ? 'selected' : ''; ?>>Vandalism</option>
                            <option value="Fraud" <?php echo ($complaint_type ?? '') == 'Fraud' ? 'selected' : ''; ?>>Fraud</option>
                            <option value="Domestic Violence" <?php echo ($complaint_type ?? '') == 'Domestic Violence' ? 'selected' : ''; ?>>Domestic Violence</option>
                            <option value="Traffic Violation" <?php echo ($complaint_type ?? '') == 'Traffic Violation' ? 'selected' : ''; ?>>Traffic Violation</option>
                            <option value="Noise Complaint" <?php echo ($complaint_type ?? '') == 'Noise Complaint' ? 'selected' : ''; ?>>Noise Complaint</option>
                            <option value="Property Damage" <?php echo ($complaint_type ?? '') == 'Property Damage' ? 'selected' : ''; ?>>Property Damage</option>
                            <option value="Missing Person" <?php echo ($complaint_type ?? '') == 'Missing Person' ? 'selected' : ''; ?>>Missing Person</option>
                            <option value="Other" <?php echo ($complaint_type ?? '') == 'Other' ? 'selected' : ''; ?>>Other</option>
                        </select>
                    </div>

                    <!-- Complaint Description -->
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Complaint Description *</label>
                        <textarea name="complaint_description" class="form-control" 
                                  placeholder="Please provide a detailed description of your complaint. Include what happened, when it happened, who was involved, and any other relevant information..."
                                  required><?php echo htmlspecialchars($complaint_description ?? ''); ?></textarea>
                    </div>

                    <!-- Incident Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Incident Date *</label>
                            <input type="date" name="incident_date" class="form-control" 
                                   value="<?php echo htmlspecialchars($incident_date ?? ''); ?>" 
                                   max="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Incident Location *</label>
                            <input type="text" name="incident_location" class="form-control" 
                                   placeholder="Enter location where incident occurred" 
                                   value="<?php echo htmlspecialchars($incident_location ?? ''); ?>" required>
                        </div>
                    </div>

                    <!-- Contact Information Notice -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-sm font-semibold mb-2">Contact Information</h3>
                        <p class="text-gray-300 text-sm">
                            We will use your registered email address (<?php echo htmlspecialchars($_SESSION['user_email']); ?>) 
                            to contact you regarding this complaint. If you need to update your contact information, 
                            please visit your profile page.
                        </p>
                    </div>

                    <div class="text-center pt-4">
                        <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300 mr-4">
                            Submit Complaint
                        </button>
                        <a href="dashboard.php" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                            Cancel
                        </a>
                    </div>
                </form>

                <div class="text-center mt-6">
                    <a href="my_complaints.php" class="text-blue-400 hover:text-blue-300 underline">
                        View My Previous Complaints
                    </a>
                    <span class="text-gray-400 mx-2">|</span>
                    <a href="dashboard.php" class="text-blue-400 hover:text-blue-300 underline">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
