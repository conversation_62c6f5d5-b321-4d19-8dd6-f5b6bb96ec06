<?php
/**
 * Database Setup Script for CRMS
 * Run this file once to create all necessary tables
 */

require_once 'database.php';

try {
    $pdo = initializeDatabase();
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            user_id INT AUTO_INCREMENT PRIMARY KEY,
            first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
            last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role ENUM('admin', 'officer', 'citizen') NOT NULL,
            service VARCHAR(100) NULL,
            phone VARCHAR(20) NULL,
            address TEXT NULL,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Create criminal_records table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS criminal_records (
            record_id INT AUTO_INCREMENT PRIMARY KEY,
            case_number VARCHAR(50) UNIQUE NOT NULL,
            suspect_name VARCHAR(100) NOT NULL,
            suspect_id_number VARCHAR(20) NULL,
            suspect_mugshot VARCHAR(255) NULL,
            crime_type VARCHAR(100) NOT NULL,
            crime_description TEXT NOT NULL,
            incident_date DATE NOT NULL,
            incident_location VARCHAR(200) NOT NULL,
            arresting_officer_id INT NULL,
            case_status ENUM('ongoing', 'solved', 'paused', 'unresolved') DEFAULT 'ongoing',
            evidence TEXT NULL,
            evidence_photos TEXT NULL,
            witness_info TEXT NULL,
            witness_statements TEXT NULL,
            court_date DATE NULL,
            verdict TEXT NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (arresting_officer_id) REFERENCES users(user_id),
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ");
    
    // Create complaints table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS complaints (
            complaint_id INT AUTO_INCREMENT PRIMARY KEY,
            complaint_number VARCHAR(50) UNIQUE NOT NULL,
            citizen_id INT NOT NULL,
            complaint_type VARCHAR(100) NOT NULL,
            complaint_description TEXT NOT NULL,
            incident_date DATE NOT NULL,
            incident_location VARCHAR(200) NOT NULL,
            status ENUM('attended', 'not_attended', 'in_progress', 'resolved') DEFAULT 'not_attended',
            assigned_officer_id INT NULL,
            response TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (citizen_id) REFERENCES users(user_id),
            FOREIGN KEY (assigned_officer_id) REFERENCES users(user_id)
        )
    ");
    
    // Create case_updates table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS case_updates (
            update_id INT AUTO_INCREMENT PRIMARY KEY,
            case_number VARCHAR(50) NOT NULL,
            update_type ENUM('status_change', 'evidence_added', 'hearing_scheduled', 'general_update') NOT NULL,
            update_description TEXT NOT NULL,
            updated_by INT NOT NULL,
            update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES users(user_id)
        )
    ");
    
    // Create hearings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS hearings (
            hearing_id INT AUTO_INCREMENT PRIMARY KEY,
            case_number VARCHAR(50) NOT NULL,
            hearing_date DATETIME NOT NULL,
            hearing_location VARCHAR(200) NOT NULL,
            hearing_type VARCHAR(100) NOT NULL,
            status ENUM('scheduled', 'completed', 'postponed', 'cancelled') DEFAULT 'scheduled',
            notes TEXT NULL,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ");
    
    // Insert default admin user
    $adminPassword = generateSecureHash('admin123');
    $pdo->exec("
        INSERT IGNORE INTO users (first_name, last_name, email, password_hash, role, service) 
        VALUES ('System', 'Administrator', '<EMAIL>', '$adminPassword', 'admin', 'Administration')
    ");
    
    echo "Database setup completed successfully!<br>";
    echo "Default admin credentials:<br>";
    echo "Email: <EMAIL><br>";
    echo "Password: admin123<br>";
    echo "<br><a href='../index.php'>Go to Home Page</a>";
    
} catch (PDOException $e) {
    die("Database setup failed: " . $e->getMessage());
}
?>
