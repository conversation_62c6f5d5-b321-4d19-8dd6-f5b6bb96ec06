<?php
require_once '../config/database.php';
session_start();
requireRole(['admin']);

// Get analytics data
try {
    $pdo = getDBConnection();
    
    // Get monthly complaints data for the last 12 months
    $stmt = $pdo->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as count
        FROM complaints 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $complaints_data = $stmt->fetchAll();
    
    // Get monthly tip-offs data for the last 12 months
    $stmt = $pdo->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as count
        FROM tip_offs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    $tipoffs_data = $stmt->fetchAll();
    
    // Get case status distribution
    $stmt = $pdo->query("
        SELECT 
            case_status,
            COUNT(*) as count
        FROM criminal_records
        GROUP BY case_status
    ");
    $case_status_data = $stmt->fetchAll();
    
    // Get crime type distribution
    $stmt = $pdo->query("
        SELECT 
            crime_type,
            COUNT(*) as count
        FROM criminal_records
        GROUP BY crime_type
        ORDER BY count DESC
        LIMIT 10
    ");
    $crime_type_data = $stmt->fetchAll();
    
    // Get user registration trends
    $stmt = $pdo->query("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            role,
            COUNT(*) as count
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m'), role
        ORDER BY month, role
    ");
    $user_registration_data = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = "Failed to load analytics data.";
}

// Prepare data for JavaScript
$complaints_months = [];
$complaints_counts = [];
$tipoffs_months = [];
$tipoffs_counts = [];

foreach ($complaints_data as $row) {
    $complaints_months[] = $row['month'];
    $complaints_counts[] = (int)$row['count'];
}

foreach ($tipoffs_data as $row) {
    $tipoffs_months[] = $row['month'];
    $tipoffs_counts[] = (int)$row['count'];
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <link href="../index.css" rel="stylesheet">
    <title>Analytics Dashboard - CRMS Admin</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        .nav-link {
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            transform: translateY(-2px);
        }
        .chart-container {
            position: relative;
            height: 400px;
            background: rgba(31, 41, 55, 0.8);
            border-radius: 8px;
            padding: 20px;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Analytics Dashboard</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Quick Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="dashboard.php" class="nav-link bg-gray-700">Dashboard</a>
                <a href="manage_users.php" class="nav-link bg-gray-700">Manage Users</a>
                <a href="manage_records.php" class="nav-link bg-gray-700">Manage Records</a>
                <a href="manage_tipoffs.php" class="nav-link bg-gray-700">Manage Tip-offs</a>
                <a href="reports.php" class="nav-link bg-gray-700">Reports</a>
                <a href="system_settings.php" class="nav-link bg-gray-700">Settings</a>
                <a href="analytics.php" class="nav-link bg-green-700">Analytics</a>
            </div>
        </div>

        <div class="space-y-8">
            <!-- Page Header -->
            <div class="form-container">
                <h2 class="text-white text-3xl font-bold mb-4">System Analytics Dashboard</h2>
                <p class="text-gray-300">Comprehensive insights into system usage, trends, and performance metrics</p>
            </div>

            <!-- Complaints and Tip-offs Trends -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Complaints Trend -->
                <div class="form-container">
                    <h3 class="text-white text-xl font-bold mb-4">Monthly Complaints Filed</h3>
                    <div class="chart-container">
                        <canvas id="complaintsChart"></canvas>
                    </div>
                </div>

                <!-- Tip-offs Trend -->
                <div class="form-container">
                    <h3 class="text-white text-xl font-bold mb-4">Monthly Tip-offs Submitted</h3>
                    <div class="chart-container">
                        <canvas id="tipoffsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Case Status and Crime Types -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Case Status Distribution -->
                <div class="form-container">
                    <h3 class="text-white text-xl font-bold mb-4">Case Status Distribution</h3>
                    <div class="chart-container">
                        <canvas id="caseStatusChart"></canvas>
                    </div>
                </div>

                <!-- Crime Types -->
                <div class="form-container">
                    <h3 class="text-white text-xl font-bold mb-4">Top Crime Types</h3>
                    <div class="chart-container">
                        <canvas id="crimeTypesChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Summary Statistics -->
            <div class="form-container">
                <h3 class="text-white text-xl font-bold mb-6">Key Performance Indicators</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <?php
                    // Calculate KPIs
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as total FROM complaints WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
                        $monthly_complaints = $stmt->fetchColumn();
                        
                        $stmt = $pdo->query("SELECT COUNT(*) as total FROM tip_offs WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
                        $monthly_tipoffs = $stmt->fetchColumn();
                        
                        $stmt = $pdo->query("SELECT COUNT(*) as total FROM criminal_records WHERE case_status = 'solved' AND MONTH(updated_at) = MONTH(NOW()) AND YEAR(updated_at) = YEAR(NOW())");
                        $monthly_solved = $stmt->fetchColumn();
                        
                        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE role = 'citizen' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
                        $monthly_registrations = $stmt->fetchColumn();
                    } catch (PDOException $e) {
                        $monthly_complaints = $monthly_tipoffs = $monthly_solved = $monthly_registrations = 0;
                    }
                    ?>
                    
                    <div class="bg-blue-600 rounded-lg p-6 text-center">
                        <h4 class="text-white text-2xl font-bold"><?php echo $monthly_complaints; ?></h4>
                        <p class="text-blue-200">Complaints This Month</p>
                    </div>
                    
                    <div class="bg-orange-600 rounded-lg p-6 text-center">
                        <h4 class="text-white text-2xl font-bold"><?php echo $monthly_tipoffs; ?></h4>
                        <p class="text-orange-200">Tip-offs This Month</p>
                    </div>
                    
                    <div class="bg-green-600 rounded-lg p-6 text-center">
                        <h4 class="text-white text-2xl font-bold"><?php echo $monthly_solved; ?></h4>
                        <p class="text-green-200">Cases Solved This Month</p>
                    </div>
                    
                    <div class="bg-purple-600 rounded-lg p-6 text-center">
                        <h4 class="text-white text-2xl font-bold"><?php echo $monthly_registrations; ?></h4>
                        <p class="text-purple-200">New Citizens This Month</p>
                    </div>
                </div>
            </div>

            <!-- Export Options -->
            <div class="form-container">
                <h3 class="text-white text-xl font-bold mb-4">Export Analytics</h3>
                <div class="flex flex-wrap gap-4">
                    <button onclick="exportChart('complaintsChart', 'complaints-trend')" 
                            class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                        Export Complaints Chart
                    </button>
                    <button onclick="exportChart('tipoffsChart', 'tipoffs-trend')" 
                            class="bg-orange-600 text-white px-6 py-2 rounded hover:bg-orange-700">
                        Export Tip-offs Chart
                    </button>
                    <button onclick="window.print()" 
                            class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700">
                        Print Dashboard
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chart.js configuration
        Chart.defaults.color = '#ffffff';
        Chart.defaults.borderColor = '#374151';

        // Complaints Chart
        const complaintsCtx = document.getElementById('complaintsChart').getContext('2d');
        new Chart(complaintsCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($complaints_months); ?>,
                datasets: [{
                    label: 'Complaints Filed',
                    data: <?php echo json_encode($complaints_counts); ?>,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#374151'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#374151'
                        }
                    }
                }
            }
        });

        // Tip-offs Chart
        const tipoffsCtx = document.getElementById('tipoffsChart').getContext('2d');
        new Chart(tipoffsCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($tipoffs_months); ?>,
                datasets: [{
                    label: 'Tip-offs Submitted',
                    data: <?php echo json_encode($tipoffs_counts); ?>,
                    backgroundColor: 'rgba(249, 115, 22, 0.8)',
                    borderColor: '#f97316',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#374151'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#374151'
                        }
                    }
                }
            }
        });

        // Case Status Chart
        const caseStatusCtx = document.getElementById('caseStatusChart').getContext('2d');
        new Chart(caseStatusCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php foreach($case_status_data as $row) echo "'" . ucfirst($row['case_status']) . "',"; ?>],
                datasets: [{
                    data: [<?php foreach($case_status_data as $row) echo $row['count'] . ","; ?>],
                    backgroundColor: [
                        '#10b981', // solved - green
                        '#f59e0b', // ongoing - yellow
                        '#3b82f6', // paused - blue
                        '#ef4444'  // unresolved - red
                    ],
                    borderWidth: 2,
                    borderColor: '#1f2937'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#ffffff',
                            padding: 20
                        }
                    }
                }
            }
        });

        // Crime Types Chart
        const crimeTypesCtx = document.getElementById('crimeTypesChart').getContext('2d');
        new Chart(crimeTypesCtx, {
            type: 'horizontalBar',
            data: {
                labels: [<?php foreach($crime_type_data as $row) echo "'" . $row['crime_type'] . "',"; ?>],
                datasets: [{
                    label: 'Number of Cases',
                    data: [<?php foreach($crime_type_data as $row) echo $row['count'] . ","; ?>],
                    backgroundColor: 'rgba(220, 38, 38, 0.8)',
                    borderColor: '#dc2626',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    y: {
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#374151'
                        }
                    },
                    x: {
                        beginAtZero: true,
                        ticks: {
                            color: '#ffffff'
                        },
                        grid: {
                            color: '#374151'
                        }
                    }
                }
            }
        });

        // Export function
        function exportChart(chartId, filename) {
            const canvas = document.getElementById(chartId);
            const url = canvas.toDataURL('image/png');
            const link = document.createElement('a');
            link.download = filename + '.png';
            link.href = url;
            link.click();
        }
    </script>
</body>
</html>
