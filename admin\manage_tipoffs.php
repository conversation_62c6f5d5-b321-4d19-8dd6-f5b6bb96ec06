<?php
require_once '../config/database.php';
session_start();
requireRole(['admin']);

$message = '';
$error_message = '';
$search_term = '';

// Handle tip-off actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = getDBConnection();
        
        if ($action == 'update_visibility' && isset($_POST['tip_id'])) {
            $tip_id = (int)$_POST['tip_id'];
            $visibility = $_POST['visibility'] ?? 'public';
            
            $stmt = $pdo->prepare("UPDATE tip_offs SET case_visibility = ? WHERE tip_id = ?");
            $stmt->execute([$visibility, $tip_id]);
            
            // Add update log
            $stmt = $pdo->prepare("
                INSERT INTO tip_off_updates (tip_number, update_type, update_description, updated_by) 
                SELECT tip_number, 'status_change', CONCAT('Case visibility changed to: ', ?), ? 
                FROM tip_offs WHERE tip_id = ?
            ");
            $stmt->execute([$visibility, $_SESSION['user_id'], $tip_id]);
            
            $message = "Tip-off visibility updated successfully.";
        }
        
        if ($action == 'assign_officer' && isset($_POST['tip_id'])) {
            $tip_id = (int)$_POST['tip_id'];
            $officer_id = (int)$_POST['officer_id'];
            
            $stmt = $pdo->prepare("UPDATE tip_offs SET assigned_officer_id = ?, status = 'under_investigation' WHERE tip_id = ?");
            $stmt->execute([$officer_id, $tip_id]);
            
            // Add update log
            $stmt = $pdo->prepare("
                INSERT INTO tip_off_updates (tip_number, update_type, update_description, updated_by) 
                SELECT tip_number, 'assignment', CONCAT('Assigned to officer ID: ', ?), ? 
                FROM tip_offs WHERE tip_id = ?
            ");
            $stmt->execute([$officer_id, $_SESSION['user_id'], $tip_id]);
            
            $message = "Officer assigned successfully.";
        }
        
        if ($action == 'update_status' && isset($_POST['tip_id'])) {
            $tip_id = (int)$_POST['tip_id'];
            $status = $_POST['status'] ?? 'new';
            $admin_notes = trim($_POST['admin_notes'] ?? '');
            
            $stmt = $pdo->prepare("UPDATE tip_offs SET status = ?, admin_notes = ? WHERE tip_id = ?");
            $stmt->execute([$status, $admin_notes, $tip_id]);
            
            // Add update log
            $stmt = $pdo->prepare("
                INSERT INTO tip_off_updates (tip_number, update_type, update_description, updated_by) 
                SELECT tip_number, 'status_change', CONCAT('Status changed to: ', ?), ? 
                FROM tip_offs WHERE tip_id = ?
            ");
            $stmt->execute([$status, $_SESSION['user_id'], $tip_id]);
            
            $message = "Tip-off status updated successfully.";
        }
        
        if ($action == 'delete' && isset($_POST['tip_id'])) {
            $tip_id = (int)$_POST['tip_id'];
            
            // Delete related updates first
            $stmt = $pdo->prepare("DELETE FROM tip_off_updates WHERE tip_number IN (SELECT tip_number FROM tip_offs WHERE tip_id = ?)");
            $stmt->execute([$tip_id]);
            
            // Delete tip-off
            $stmt = $pdo->prepare("DELETE FROM tip_offs WHERE tip_id = ?");
            $stmt->execute([$tip_id]);
            
            $message = "Tip-off deleted successfully.";
        }
        
    } catch (PDOException $e) {
        $error_message = "Operation failed: " . $e->getMessage();
    }
}

// Handle search
if (isset($_GET['search'])) {
    $search_term = trim($_GET['search']);
}

// Get tip-offs
try {
    $pdo = getDBConnection();
    
    // Get officers for assignment dropdown
    $stmt = $pdo->query("SELECT user_id, first_name, last_name FROM users WHERE role = 'officer' AND status = 'active' ORDER BY first_name, last_name");
    $officers = $stmt->fetchAll();
    
    // Get tip-offs with search
    if (!empty($search_term)) {
        $stmt = $pdo->prepare("
            SELECT t.*, u.first_name, u.last_name, o.first_name as officer_first_name, o.last_name as officer_last_name
            FROM tip_offs t 
            LEFT JOIN users u ON t.citizen_id = u.user_id 
            LEFT JOIN users o ON t.assigned_officer_id = o.user_id
            WHERE t.tip_number LIKE ? OR t.tip_type LIKE ? OR t.tip_description LIKE ?
            ORDER BY t.created_at DESC
        ");
        $search_param = "%$search_term%";
        $stmt->execute([$search_param, $search_param, $search_param]);
    } else {
        $stmt = $pdo->query("
            SELECT t.*, u.first_name, u.last_name, o.first_name as officer_first_name, o.last_name as officer_last_name
            FROM tip_offs t 
            LEFT JOIN users u ON t.citizen_id = u.user_id 
            LEFT JOIN users o ON t.assigned_officer_id = o.user_id
            ORDER BY t.created_at DESC
        ");
    }
    $tipoffs = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = "Failed to load tip-offs.";
    $tipoffs = [];
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <link href="../index.css" rel="stylesheet">
    <title>Manage Tip-offs - CRMS Admin</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        .form-control {
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 8px 12px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
        }
        .nav-link {
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-new { background-color: #3b82f6; color: white; }
        .status-under_investigation { background-color: #f59e0b; color: white; }
        .status-resolved { background-color: #10b981; color: white; }
        .status-dismissed { background-color: #6b7280; color: white; }
        .visibility-public { background-color: #10b981; color: white; }
        .visibility-private { background-color: #ef4444; color: white; }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Manage Tip-offs</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Quick Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="dashboard.php" class="nav-link bg-gray-700">Dashboard</a>
                <a href="manage_users.php" class="nav-link bg-gray-700">Manage Users</a>
                <a href="manage_records.php" class="nav-link bg-gray-700">Manage Records</a>
                <a href="manage_tipoffs.php" class="nav-link bg-green-700">Manage Tip-offs</a>
                <a href="reports.php" class="nav-link bg-gray-700">Reports</a>
                <a href="system_settings.php" class="nav-link bg-gray-700">Settings</a>
            </div>
        </div>

        <div class="form-container">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-white text-2xl font-bold">Tip-offs Management</h2>
                
                <!-- Search -->
                <form method="GET" action="" class="flex gap-2">
                    <input type="text" name="search" placeholder="Search tip-offs..." 
                           value="<?php echo htmlspecialchars($search_term); ?>"
                           class="form-control w-64">
                    <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                        Search
                    </button>
                    <?php if (!empty($search_term)): ?>
                        <a href="manage_tipoffs.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                            Clear
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            
            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($message)): ?>
                <div class="bg-green-600 text-white p-4 rounded-lg mb-6">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <?php
                $stats = ['new' => 0, 'under_investigation' => 0, 'resolved' => 0, 'dismissed' => 0];
                foreach ($tipoffs as $tip) {
                    $stats[$tip['status']]++;
                }
                ?>
                <div class="bg-blue-600 rounded-lg p-4 text-center">
                    <h3 class="text-white text-lg font-bold"><?php echo $stats['new']; ?></h3>
                    <p class="text-blue-200">New Tips</p>
                </div>
                <div class="bg-yellow-600 rounded-lg p-4 text-center">
                    <h3 class="text-white text-lg font-bold"><?php echo $stats['under_investigation']; ?></h3>
                    <p class="text-yellow-200">Under Investigation</p>
                </div>
                <div class="bg-green-600 rounded-lg p-4 text-center">
                    <h3 class="text-white text-lg font-bold"><?php echo $stats['resolved']; ?></h3>
                    <p class="text-green-200">Resolved</p>
                </div>
                <div class="bg-gray-600 rounded-lg p-4 text-center">
                    <h3 class="text-white text-lg font-bold"><?php echo $stats['dismissed']; ?></h3>
                    <p class="text-gray-200">Dismissed</p>
                </div>
            </div>

            <!-- Tip-offs Table -->
            <div class="overflow-x-auto">
                <table class="w-full text-white text-sm">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-3 px-2">Tip Number</th>
                            <th class="text-left py-3 px-2">Type</th>
                            <th class="text-left py-3 px-2">Reporter</th>
                            <th class="text-left py-3 px-2">Status</th>
                            <th class="text-left py-3 px-2">Visibility</th>
                            <th class="text-left py-3 px-2">Assigned Officer</th>
                            <th class="text-left py-3 px-2">Date</th>
                            <th class="text-left py-3 px-2">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($tipoffs)): ?>
                            <tr>
                                <td colspan="8" class="text-center py-8 text-gray-400">
                                    No tip-offs found.
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($tipoffs as $tip): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-800">
                                    <td class="py-3 px-2">
                                        <span class="font-semibold"><?php echo htmlspecialchars($tip['tip_number']); ?></span>
                                    </td>
                                    <td class="py-3 px-2"><?php echo htmlspecialchars($tip['tip_type']); ?></td>
                                    <td class="py-3 px-2">
                                        <?php if ($tip['reporter_type'] == 'anonymous'): ?>
                                            <span class="text-gray-400">Anonymous</span>
                                        <?php else: ?>
                                            <?php echo htmlspecialchars($tip['first_name'] . ' ' . $tip['last_name']); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-3 px-2">
                                        <span class="status-badge status-<?php echo $tip['status']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $tip['status'])); ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-2">
                                        <span class="status-badge visibility-<?php echo $tip['case_visibility']; ?>">
                                            <?php echo ucfirst($tip['case_visibility']); ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-2">
                                        <?php if ($tip['assigned_officer_id']): ?>
                                            <?php echo htmlspecialchars($tip['officer_first_name'] . ' ' . $tip['officer_last_name']); ?>
                                        <?php else: ?>
                                            <span class="text-gray-400">Unassigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-3 px-2"><?php echo date('M d, Y', strtotime($tip['created_at'])); ?></td>
                                    <td class="py-3 px-2">
                                        <div class="flex gap-1">
                                            <button onclick="viewTip(<?php echo $tip['tip_id']; ?>)" 
                                                    class="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700">
                                                View
                                            </button>
                                            <button onclick="editTip(<?php echo $tip['tip_id']; ?>)" 
                                                    class="bg-yellow-600 text-white px-2 py-1 rounded text-xs hover:bg-yellow-700">
                                                Edit
                                            </button>
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirm('Are you sure you want to delete this tip-off?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="tip_id" value="<?php echo $tip['tip_id']; ?>">
                                                <button type="submit" class="bg-red-600 text-white px-2 py-1 rounded text-xs hover:bg-red-700">
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Tip Details Modal (placeholder for future implementation) -->
    <div id="tipModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
            <div id="tipModalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        function viewTip(tipId) {
            // Implementation for viewing tip details
            alert('View tip functionality - to be implemented');
        }
        
        function editTip(tipId) {
            // Implementation for editing tip
            alert('Edit tip functionality - to be implemented');
        }
    </script>
</body>
</html>
