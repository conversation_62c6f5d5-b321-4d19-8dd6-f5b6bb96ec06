<?php
require_once '../config/database.php';
session_start();
requireRole(['officer']);

$message = '';
$error_message = '';

// Get current user data
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        header('Location: ../config/logout.php');
        exit();
    }
} catch (PDOException $e) {
    $error_message = "Failed to load profile data.";
}

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $service = trim($_POST['service'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($first_name) || empty($last_name) || empty($email)) {
        $error_message = "Please fill in all required fields.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = "Please enter a valid email address.";
    } else {
        try {
            $pdo = getDBConnection();
            
            // Check if email is already taken by another user
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $_SESSION['user_id']]);
            if ($stmt->fetch()) {
                $error_message = "Email address is already in use by another user.";
            } else {
                // Update basic profile information
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET first_name = ?, last_name = ?, email = ?, phone = ?, address = ?, service = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ?
                ");
                $stmt->execute([$first_name, $last_name, $email, $phone, $address, $service, $_SESSION['user_id']]);
                
                // Handle password change if requested
                if (!empty($new_password)) {
                    if (empty($current_password)) {
                        $error_message = "Current password is required to change password.";
                    } elseif ($new_password !== $confirm_password) {
                        $error_message = "New passwords do not match.";
                    } elseif (strlen($new_password) < 6) {
                        $error_message = "New password must be at least 6 characters long.";
                    } else {
                        // Verify current password
                        if (verifyPassword($current_password, $user['password_hash'])) {
                            $new_password_hash = generateSecureHash($new_password);
                            $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE user_id = ?");
                            $stmt->execute([$new_password_hash, $_SESSION['user_id']]);
                            $message = "Profile and password updated successfully!";
                        } else {
                            $error_message = "Current password is incorrect.";
                        }
                    }
                } else {
                    $message = "Profile updated successfully!";
                }
                
                // Update session data
                if (empty($error_message)) {
                    $_SESSION['user_name'] = $first_name . ' ' . $last_name;
                    $_SESSION['user_email'] = $email;
                    
                    // Refresh user data
                    $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
                    $stmt->execute([$_SESSION['user_id']]);
                    $user = $stmt->fetch();
                }
            }
        } catch (PDOException $e) {
            $error_message = "Failed to update profile. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <link href="../index.css" rel="stylesheet">
    <title>My Profile - CRMS Officer</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 8px 12px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
        }
        .nav-link {
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - My Profile</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Officer <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Quick Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="dashboard.php" class="nav-link bg-gray-700">Dashboard</a>
                <a href="new_record.php" class="nav-link bg-gray-700">File New Record</a>
                <a href="manage_records.php" class="nav-link bg-gray-700">My Cases</a>
                <a href="reports.php" class="nav-link bg-gray-700">Generate Reports</a>
                <a href="profile.php" class="nav-link bg-green-700">My Profile</a>
            </div>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="form-container">
                <h2 class="text-white text-2xl font-bold mb-6">Officer Profile</h2>
                
                <?php if (!empty($error_message)): ?>
                    <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($message)): ?>
                    <div class="bg-green-600 text-white p-4 rounded-lg mb-6">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" class="space-y-6">
                    <!-- Personal Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Personal Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">First Name *</label>
                                <input type="text" name="first_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Last Name *</label>
                                <input type="text" name="last_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Contact Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Email Address *</label>
                                <input type="email" name="email" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Phone Number</label>
                                <input type="text" name="phone" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="text-white text-sm font-semibold mb-2 block">Address</label>
                            <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Service Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Service Information</h3>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Service/Department</label>
                            <input type="text" name="service" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['service'] ?? ''); ?>"
                                   placeholder="e.g., Criminal Investigation, Traffic Police, etc.">
                        </div>
                    </div>

                    <!-- Password Change -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Change Password (Optional)</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Current Password</label>
                                <input type="password" name="current_password" class="form-control">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">New Password</label>
                                <input type="password" name="new_password" class="form-control">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Confirm New Password</label>
                                <input type="password" name="confirm_password" class="form-control">
                            </div>
                        </div>
                        <p class="text-gray-400 text-sm mt-2">Leave password fields empty if you don't want to change your password.</p>
                    </div>

                    <!-- Account Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Account Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-300">
                            <div>
                                <p><strong>Officer ID:</strong> <?php echo $user['user_id']; ?></p>
                                <p><strong>Role:</strong> <?php echo ucfirst($user['role']); ?></p>
                            </div>
                            <div>
                                <p><strong>Account Created:</strong> <?php echo date('M d, Y', strtotime($user['created_at'])); ?></p>
                                <p><strong>Last Updated:</strong> <?php echo date('M d, Y H:i', strtotime($user['updated_at'])); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="text-center pt-4">
                        <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300 mr-4">
                            Update Profile
                        </button>
                        <a href="dashboard.php" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
