<?php
require_once '../config/database.php';
session_start();
requireRole(['admin']);

$user = null;
$message = '';
$error_message = '';

// Get user ID
if (!isset($_GET['id'])) {
    header('Location: manage_users.php');
    exit();
}

$user_id = (int)$_GET['id'];

// Load existing user
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        $error_message = "User not found.";
    }
} catch (PDOException $e) {
    $error_message = "Failed to load user.";
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $user) {
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $email = sanitizeInput($_POST['email']);
    $role = $_POST['role'];
    $service = sanitizeInput($_POST['service']);
    $phone = sanitizeInput($_POST['phone']);
    $address = sanitizeInput($_POST['address']);
    $status = $_POST['status'];
    $new_password = $_POST['new_password'];
    
    // Validation
    if (empty($first_name) || empty($last_name) || empty($email) || empty($role)) {
        $error_message = "Please fill in all required fields.";
    } else {
        try {
            $pdo = getDBConnection();
            
            // Check if email already exists for other users
            $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
            $stmt->execute([$email, $user_id]);
            if ($stmt->fetch()) {
                $error_message = "Email address already exists for another user.";
            } else {
                // Prepare update query
                if (!empty($new_password)) {
                    // Update with new password
                    $password_hash = generateSecureHash($new_password);
                    $stmt = $pdo->prepare("
                        UPDATE users SET 
                            first_name = ?, last_name = ?, email = ?, role = ?, 
                            service = ?, phone = ?, address = ?, status = ?, password_hash = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$first_name, $last_name, $email, $role, $service, $phone, $address, $status, $password_hash, $user_id]);
                } else {
                    // Update without changing password
                    $stmt = $pdo->prepare("
                        UPDATE users SET 
                            first_name = ?, last_name = ?, email = ?, role = ?, 
                            service = ?, phone = ?, address = ?, status = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE user_id = ?
                    ");
                    $stmt->execute([$first_name, $last_name, $email, $role, $service, $phone, $address, $status, $user_id]);
                }
                
                $message = "User updated successfully!";
                
                // Reload user data
                $stmt = $pdo->prepare("SELECT * FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch();
            }
        } catch (PDOException $e) {
            $error_message = "Failed to update user. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Edit User - CRMS Admin</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 30px;
        }
        .form-control {
            width: 100%;
            height: 45px;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 0 15px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Edit User</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="manage_users.php" class="text-white hover:text-gray-300">Manage Users</a>
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($error_message); ?>
                <div class="mt-4">
                    <a href="manage_users.php" class="underline">Return to User Management</a>
                </div>
            </div>
        <?php elseif ($user): ?>
            <div class="max-w-2xl mx-auto">
                <!-- Messages -->
                <?php if (!empty($message)): ?>
                    <div class="bg-green-600 text-white p-4 rounded mb-6">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <div class="form-container">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-white text-2xl font-bold">Edit User</h2>
                        <span class="text-red-400">ID: <?php echo $user['user_id']; ?></span>
                    </div>
                    
                    <form method="POST" action="" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">First Name *</label>
                                <input type="text" name="first_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Last Name *</label>
                                <input type="text" name="last_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                            </div>
                        </div>

                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Email Address *</label>
                            <input type="email" name="email" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Role *</label>
                                <select name="role" class="form-control" required>
                                    <option value="admin" <?php echo $user['role'] == 'admin' ? 'selected' : ''; ?>>Administrator</option>
                                    <option value="officer" <?php echo $user['role'] == 'officer' ? 'selected' : ''; ?>>Police Officer</option>
                                    <option value="citizen" <?php echo $user['role'] == 'citizen' ? 'selected' : ''; ?>>Citizen</option>
                                </select>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Status *</label>
                                <select name="status" class="form-control" required>
                                    <option value="active" <?php echo $user['status'] == 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $user['status'] == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="suspended" <?php echo $user['status'] == 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Service/Department</label>
                            <select name="service" class="form-control">
                                <option value="">Select Service</option>
                                <option value="Criminal Investigation" <?php echo $user['service'] == 'Criminal Investigation' ? 'selected' : ''; ?>>Criminal Investigation</option>
                                <option value="Traffic Police" <?php echo $user['service'] == 'Traffic Police' ? 'selected' : ''; ?>>Traffic Police</option>
                                <option value="Community Policing" <?php echo $user['service'] == 'Community Policing' ? 'selected' : ''; ?>>Community Policing</option>
                                <option value="Special Operations" <?php echo $user['service'] == 'Special Operations' ? 'selected' : ''; ?>>Special Operations</option>
                                <option value="Forensics" <?php echo $user['service'] == 'Forensics' ? 'selected' : ''; ?>>Forensics</option>
                                <option value="Administration" <?php echo $user['service'] == 'Administration' ? 'selected' : ''; ?>>Administration</option>
                            </select>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Phone Number</label>
                                <input type="tel" name="phone" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Address</label>
                                <input type="text" name="address" class="form-control" 
                                       value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>">
                            </div>
                        </div>

                        <!-- Password Section -->
                        <div class="bg-gray-700 rounded-lg p-4 mt-6">
                            <h3 class="text-white text-lg font-semibold mb-4">Change Password (Optional)</h3>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">New Password</label>
                                <input type="password" name="new_password" class="form-control" 
                                       placeholder="Leave blank to keep current password">
                                <p class="text-gray-400 text-xs mt-1">Minimum 6 characters required if changing password</p>
                            </div>
                        </div>

                        <!-- User Information -->
                        <div class="bg-gray-700 rounded-lg p-4 mt-6">
                            <h3 class="text-white text-lg font-semibold mb-4">Account Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-300">Created: <span class="text-white"><?php echo date('M d, Y H:i', strtotime($user['created_at'])); ?></span></p>
                                    <p class="text-gray-300">Last Updated: <span class="text-white"><?php echo date('M d, Y H:i', strtotime($user['updated_at'])); ?></span></p>
                                </div>
                                <div>
                                    <p class="text-gray-300">User ID: <span class="text-white"><?php echo $user['user_id']; ?></span></p>
                                    <p class="text-gray-300">Current Role: <span class="text-white"><?php echo ucfirst($user['role']); ?></span></p>
                                </div>
                            </div>
                        </div>

                        <div class="text-center pt-6">
                            <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300 mr-4">
                                Update User
                            </button>
                            <a href="manage_users.php" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                                Cancel
                            </a>
                        </div>
                    </form>

                    <div class="text-center mt-6">
                        <a href="manage_users.php" class="text-blue-400 hover:text-blue-300 underline">
                            Back to User Management
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
