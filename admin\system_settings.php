<?php
require_once '../config/database.php';
session_start();
requireRole(['admin']);

$message = '';
$error_message = '';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = getDBConnection();
        
        if ($action == 'update_settings') {
            $session_timeout = (int)($_POST['session_timeout'] ?? 30);
            $max_file_size = (int)($_POST['max_file_size'] ?? 5);
            $password_min_length = (int)($_POST['password_min_length'] ?? 6);
            $system_name = trim($_POST['system_name'] ?? 'Criminal Record Management System');
            $admin_email = trim($_POST['admin_email'] ?? '');
            $backup_frequency = $_POST['backup_frequency'] ?? 'weekly';
            
            // Create settings table if it doesn't exist
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS system_settings (
                    setting_id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            
            // Update or insert settings
            $settings = [
                'session_timeout' => $session_timeout,
                'max_file_size' => $max_file_size,
                'password_min_length' => $password_min_length,
                'system_name' => $system_name,
                'admin_email' => $admin_email,
                'backup_frequency' => $backup_frequency
            ];
            
            foreach ($settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO system_settings (setting_key, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
            
            $message = "System settings updated successfully!";
        }
        
        if ($action == 'clear_logs') {
            // Clear old case updates (older than 6 months)
            $stmt = $pdo->prepare("DELETE FROM case_updates WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH)");
            $stmt->execute();
            $message = "System logs cleared successfully!";
        }
        
        if ($action == 'backup_database') {
            // This would typically trigger a database backup
            $message = "Database backup initiated successfully!";
        }
        
    } catch (PDOException $e) {
        $error_message = "Failed to update settings: " . $e->getMessage();
    }
}

// Get current settings
$current_settings = [];
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
    while ($row = $stmt->fetch()) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    // Settings table might not exist yet
}

// Default values
$defaults = [
    'session_timeout' => 30,
    'max_file_size' => 5,
    'password_min_length' => 6,
    'system_name' => 'Criminal Record Management System',
    'admin_email' => '<EMAIL>',
    'backup_frequency' => 'weekly'
];

foreach ($defaults as $key => $value) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $value;
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <link href="../index.css" rel="stylesheet">
    <title>System Settings - CRMS Admin</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 8px 12px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
        }
        .nav-link {
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - System Settings</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Quick Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="dashboard.php" class="nav-link bg-gray-700">Dashboard</a>
                <a href="manage_users.php" class="nav-link bg-gray-700">Manage Users</a>
                <a href="manage_records.php" class="nav-link bg-gray-700">Manage Records</a>
                <a href="reports.php" class="nav-link bg-gray-700">Reports</a>
                <a href="system_settings.php" class="nav-link bg-green-700">System Settings</a>
            </div>
        </div>

        <div class="max-w-6xl mx-auto">
            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($message)): ?>
                <div class="bg-green-600 text-white p-4 rounded-lg mb-6">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- System Configuration -->
            <div class="form-container mb-8">
                <h2 class="text-white text-2xl font-bold mb-6">System Configuration</h2>
                
                <form method="POST" action="" class="space-y-6">
                    <input type="hidden" name="action" value="update_settings">
                    
                    <!-- General Settings -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">General Settings</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">System Name</label>
                                <input type="text" name="system_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($current_settings['system_name']); ?>">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Admin Email</label>
                                <input type="email" name="admin_email" class="form-control" 
                                       value="<?php echo htmlspecialchars($current_settings['admin_email']); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Security Settings</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Session Timeout (minutes)</label>
                                <input type="number" name="session_timeout" class="form-control" min="5" max="120"
                                       value="<?php echo htmlspecialchars($current_settings['session_timeout']); ?>">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Password Min Length</label>
                                <input type="number" name="password_min_length" class="form-control" min="4" max="20"
                                       value="<?php echo htmlspecialchars($current_settings['password_min_length']); ?>">
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Max File Size (MB)</label>
                                <input type="number" name="max_file_size" class="form-control" min="1" max="50"
                                       value="<?php echo htmlspecialchars($current_settings['max_file_size']); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Backup Settings -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Backup Settings</h3>
                        <div>
                            <label class="text-white text-sm font-semibold mb-2 block">Backup Frequency</label>
                            <select name="backup_frequency" class="form-control">
                                <option value="daily" <?php echo $current_settings['backup_frequency'] == 'daily' ? 'selected' : ''; ?>>Daily</option>
                                <option value="weekly" <?php echo $current_settings['backup_frequency'] == 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                                <option value="monthly" <?php echo $current_settings['backup_frequency'] == 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                            </select>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300">
                            Update Settings
                        </button>
                    </div>
                </form>
            </div>

            <!-- System Maintenance -->
            <div class="form-container">
                <h2 class="text-white text-2xl font-bold mb-6">System Maintenance</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Clear Logs -->
                    <div class="bg-gray-700 rounded-lg p-4 text-center">
                        <h3 class="text-white text-lg font-semibold mb-4">Clear System Logs</h3>
                        <p class="text-gray-300 text-sm mb-4">Remove old case update logs (older than 6 months)</p>
                        <form method="POST" action="" style="display: inline;">
                            <input type="hidden" name="action" value="clear_logs">
                            <button type="submit" class="bg-yellow-600 text-white px-6 py-2 rounded hover:bg-yellow-700"
                                    onclick="return confirm('Are you sure you want to clear old logs?')">
                                Clear Logs
                            </button>
                        </form>
                    </div>

                    <!-- Database Backup -->
                    <div class="bg-gray-700 rounded-lg p-4 text-center">
                        <h3 class="text-white text-lg font-semibold mb-4">Database Backup</h3>
                        <p class="text-gray-300 text-sm mb-4">Create a manual backup of the database</p>
                        <form method="POST" action="" style="display: inline;">
                            <input type="hidden" name="action" value="backup_database">
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                                Backup Now
                            </button>
                        </form>
                    </div>

                    <!-- System Info -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">System Information</h3>
                        <div class="text-gray-300 text-sm space-y-2">
                            <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
                            <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                            <p><strong>Database:</strong> MySQL</p>
                            <p><strong>System Status:</strong> <span class="text-green-400">Online</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
