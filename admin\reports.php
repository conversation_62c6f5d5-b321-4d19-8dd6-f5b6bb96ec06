<?php
require_once '../config/database.php';
session_start();
requireRole(['admin']);

$message = '';
$error_message = '';
$report_data = null;
$report_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $report_type = $_POST['report_type'];
    $date_from = $_POST['date_from'] ?? '';
    $date_to = $_POST['date_to'] ?? '';
    $status_filter = $_POST['status_filter'] ?? '';
    
    try {
        $pdo = getDBConnection();
        
        switch ($report_type) {
            case 'cases_summary':
                $sql = "SELECT 
                    case_status,
                    COUNT(*) as count,
                    crime_type,
                    DATE(created_at) as date_created
                    FROM criminal_records 
                    WHERE 1=1";
                
                $params = [];
                if (!empty($date_from)) {
                    $sql .= " AND DATE(created_at) >= ?";
                    $params[] = $date_from;
                }
                if (!empty($date_to)) {
                    $sql .= " AND DATE(created_at) <= ?";
                    $params[] = $date_to;
                }
                if (!empty($status_filter)) {
                    $sql .= " AND case_status = ?";
                    $params[] = $status_filter;
                }
                
                $sql .= " GROUP BY case_status, crime_type ORDER BY count DESC";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $report_data = $stmt->fetchAll();
                break;
                
            case 'monthly_stats':
                $sql = "SELECT 
                    YEAR(created_at) as year,
                    MONTH(created_at) as month,
                    MONTHNAME(created_at) as month_name,
                    case_status,
                    COUNT(*) as count
                    FROM criminal_records 
                    WHERE 1=1";
                
                $params = [];
                if (!empty($date_from)) {
                    $sql .= " AND DATE(created_at) >= ?";
                    $params[] = $date_from;
                }
                if (!empty($date_to)) {
                    $sql .= " AND DATE(created_at) <= ?";
                    $params[] = $date_to;
                }
                
                $sql .= " GROUP BY year, month, case_status ORDER BY year DESC, month DESC";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $report_data = $stmt->fetchAll();
                break;
                
            case 'officer_performance':
                $sql = "SELECT 
                    u.first_name,
                    u.last_name,
                    u.email,
                    COUNT(cr.record_id) as total_cases,
                    SUM(CASE WHEN cr.case_status = 'solved' THEN 1 ELSE 0 END) as solved_cases,
                    SUM(CASE WHEN cr.case_status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_cases,
                    SUM(CASE WHEN cr.case_status = 'unresolved' THEN 1 ELSE 0 END) as unresolved_cases
                    FROM users u
                    LEFT JOIN criminal_records cr ON u.user_id = cr.created_by
                    WHERE u.role = 'officer' AND u.status = 'active'";
                
                $params = [];
                if (!empty($date_from)) {
                    $sql .= " AND (cr.created_at IS NULL OR DATE(cr.created_at) >= ?)";
                    $params[] = $date_from;
                }
                if (!empty($date_to)) {
                    $sql .= " AND (cr.created_at IS NULL OR DATE(cr.created_at) <= ?)";
                    $params[] = $date_to;
                }
                
                $sql .= " GROUP BY u.user_id ORDER BY total_cases DESC";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $report_data = $stmt->fetchAll();
                break;
                
            case 'crime_types':
                $sql = "SELECT 
                    crime_type,
                    COUNT(*) as count,
                    case_status,
                    AVG(DATEDIFF(COALESCE(updated_at, CURRENT_DATE), created_at)) as avg_days_to_resolve
                    FROM criminal_records 
                    WHERE 1=1";
                
                $params = [];
                if (!empty($date_from)) {
                    $sql .= " AND DATE(created_at) >= ?";
                    $params[] = $date_from;
                }
                if (!empty($date_to)) {
                    $sql .= " AND DATE(created_at) <= ?";
                    $params[] = $date_to;
                }
                
                $sql .= " GROUP BY crime_type, case_status ORDER BY count DESC";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $report_data = $stmt->fetchAll();
                break;
        }
        
        if (empty($report_data)) {
            $message = "No data found for the selected criteria.";
        }
        
    } catch (PDOException $e) {
        $error_message = "Failed to generate report. Please try again.";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Generate Reports - CRMS Admin</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 8px 12px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
        }
        @media print {
            .no-print { display: none; }
            body { background: white !important; }
            .bg-gray-800 { background: white !important; color: black !important; }
            .text-white { color: black !important; }
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4 no-print">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Generate Reports</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <!-- Messages -->
        <?php if (!empty($message)): ?>
            <div class="bg-green-600 text-white p-4 rounded mb-6 no-print">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-600 text-white p-4 rounded mb-6 no-print">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- Report Generation Form -->
        <div class="form-container mb-8 no-print">
            <h2 class="text-white text-xl font-bold mb-4">Generate Report</h2>
            
            <form method="POST" action="" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Report Type</label>
                        <select name="report_type" class="form-control" required>
                            <option value="">Select Report Type</option>
                            <option value="cases_summary" <?php echo $report_type == 'cases_summary' ? 'selected' : ''; ?>>Cases Summary</option>
                            <option value="monthly_stats" <?php echo $report_type == 'monthly_stats' ? 'selected' : ''; ?>>Monthly Statistics</option>
                            <option value="officer_performance" <?php echo $report_type == 'officer_performance' ? 'selected' : ''; ?>>Officer Performance</option>
                            <option value="crime_types" <?php echo $report_type == 'crime_types' ? 'selected' : ''; ?>>Crime Types Analysis</option>
                        </select>
                    </div>
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">From Date</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($date_from ?? ''); ?>">
                    </div>
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">To Date</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($date_to ?? ''); ?>">
                    </div>
                    <div>
                        <label class="text-white text-sm font-semibold mb-2 block">Status Filter</label>
                        <select name="status_filter" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="ongoing" <?php echo ($status_filter ?? '') == 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                            <option value="solved" <?php echo ($status_filter ?? '') == 'solved' ? 'selected' : ''; ?>>Solved</option>
                            <option value="paused" <?php echo ($status_filter ?? '') == 'paused' ? 'selected' : ''; ?>>Paused</option>
                            <option value="unresolved" <?php echo ($status_filter ?? '') == 'unresolved' ? 'selected' : ''; ?>>Unresolved</option>
                        </select>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" class="bg-red-600 text-white px-6 py-2 rounded hover:bg-red-700 mr-4">
                        Generate Report
                    </button>
                    <?php if (!empty($report_data)): ?>
                        <button type="button" onclick="window.print()" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                            Print Report
                        </button>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Report Results -->
        <?php if (!empty($report_data)): ?>
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-white text-2xl font-bold">
                        <?php 
                        switch($report_type) {
                            case 'cases_summary': echo 'Cases Summary Report'; break;
                            case 'monthly_stats': echo 'Monthly Statistics Report'; break;
                            case 'officer_performance': echo 'Officer Performance Report'; break;
                            case 'crime_types': echo 'Crime Types Analysis Report'; break;
                        }
                        ?>
                    </h2>
                    <p class="text-gray-300">
                        Generated on: <?php echo date('F d, Y H:i'); ?>
                        <?php if (!empty($date_from) || !empty($date_to)): ?>
                            <br>Period: 
                            <?php echo !empty($date_from) ? date('M d, Y', strtotime($date_from)) : 'Beginning'; ?> - 
                            <?php echo !empty($date_to) ? date('M d, Y', strtotime($date_to)) : 'Present'; ?>
                        <?php endif; ?>
                    </p>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-white text-sm">
                        <thead>
                            <tr class="border-b border-gray-600">
                                <?php if ($report_type == 'cases_summary'): ?>
                                    <th class="text-left py-2">Status</th>
                                    <th class="text-left py-2">Crime Type</th>
                                    <th class="text-left py-2">Count</th>
                                    <th class="text-left py-2">Date Created</th>
                                <?php elseif ($report_type == 'monthly_stats'): ?>
                                    <th class="text-left py-2">Year</th>
                                    <th class="text-left py-2">Month</th>
                                    <th class="text-left py-2">Status</th>
                                    <th class="text-left py-2">Count</th>
                                <?php elseif ($report_type == 'officer_performance'): ?>
                                    <th class="text-left py-2">Officer Name</th>
                                    <th class="text-left py-2">Email</th>
                                    <th class="text-left py-2">Total Cases</th>
                                    <th class="text-left py-2">Solved</th>
                                    <th class="text-left py-2">Ongoing</th>
                                    <th class="text-left py-2">Unresolved</th>
                                <?php elseif ($report_type == 'crime_types'): ?>
                                    <th class="text-left py-2">Crime Type</th>
                                    <th class="text-left py-2">Status</th>
                                    <th class="text-left py-2">Count</th>
                                    <th class="text-left py-2">Avg Days to Resolve</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($report_data as $row): ?>
                                <tr class="border-b border-gray-700">
                                    <?php if ($report_type == 'cases_summary'): ?>
                                        <td class="py-2"><?php echo ucfirst($row['case_status']); ?></td>
                                        <td class="py-2"><?php echo htmlspecialchars($row['crime_type']); ?></td>
                                        <td class="py-2"><?php echo $row['count']; ?></td>
                                        <td class="py-2"><?php echo date('M d, Y', strtotime($row['date_created'])); ?></td>
                                    <?php elseif ($report_type == 'monthly_stats'): ?>
                                        <td class="py-2"><?php echo $row['year']; ?></td>
                                        <td class="py-2"><?php echo $row['month_name']; ?></td>
                                        <td class="py-2"><?php echo ucfirst($row['case_status']); ?></td>
                                        <td class="py-2"><?php echo $row['count']; ?></td>
                                    <?php elseif ($report_type == 'officer_performance'): ?>
                                        <td class="py-2"><?php echo htmlspecialchars($row['first_name'] . ' ' . $row['last_name']); ?></td>
                                        <td class="py-2"><?php echo htmlspecialchars($row['email']); ?></td>
                                        <td class="py-2"><?php echo $row['total_cases']; ?></td>
                                        <td class="py-2"><?php echo $row['solved_cases']; ?></td>
                                        <td class="py-2"><?php echo $row['ongoing_cases']; ?></td>
                                        <td class="py-2"><?php echo $row['unresolved_cases']; ?></td>
                                    <?php elseif ($report_type == 'crime_types'): ?>
                                        <td class="py-2"><?php echo htmlspecialchars($row['crime_type']); ?></td>
                                        <td class="py-2"><?php echo ucfirst($row['case_status']); ?></td>
                                        <td class="py-2"><?php echo $row['count']; ?></td>
                                        <td class="py-2"><?php echo round($row['avg_days_to_resolve'], 1); ?> days</td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
